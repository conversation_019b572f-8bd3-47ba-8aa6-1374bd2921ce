-- =====================================================
-- KeyAuth Database Migration: Step-by-Step Approach
-- =====================================================
-- This script migrates your database step by step with better error handling

-- =====================================================
-- STEP 1: CHECK CURRENT DATABASE STRUCTURE
-- =====================================================

-- Check what tables exist
DO $$
BEGIN
    RAISE NOTICE '=== CHECKING CURRENT DATABASE STRUCTURE ===';
    
    -- Check if license_keys table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'license_keys') THEN
        RAISE NOTICE '✓ license_keys table exists';
    ELSE
        RAISE EXCEPTION 'license_keys table not found! Please check your database.';
    END IF;
    
    -- Check if auth.users exists (Supabase)
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'users') THEN
        RAISE NOTICE '✓ auth.users table exists (Supabase detected)';
    ELSE
        RAISE NOTICE '⚠ auth.users not found - you may need to adjust user references';
    END IF;
END $$;

-- Show current license_keys structure
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'license_keys' 
ORDER BY ordinal_position;

-- =====================================================
-- STEP 2: CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to generate app_id
CREATE OR REPLACE FUNCTION generate_app_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'app_' || substr(md5(random()::text || clock_timestamp()::text), 1, 8);
END;
$$ LANGUAGE plpgsql;

-- Function to generate app_secret
CREATE OR REPLACE FUNCTION generate_app_secret()
RETURNS TEXT AS $$
BEGIN
    RETURN 'sk_' || substr(md5(random()::text || clock_timestamp()::text), 1, 32);
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- STEP 3: CREATE APPLICATIONS TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL, -- We'll add the foreign key constraint later
    app_id VARCHAR(50) UNIQUE NOT NULL,
    app_secret VARCHAR(100) UNIQUE NOT NULL,
    app_name VARCHAR(255) NOT NULL,
    app_description TEXT,
    platform VARCHAR(50) DEFAULT 'desktop',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- App settings
    hwid_method VARCHAR(50) DEFAULT 'comprehensive',
    cache_duration_seconds INTEGER DEFAULT 3,
    discord_support VARCHAR(255),
    
    -- Usage statistics
    total_validations INTEGER DEFAULT 0,
    successful_validations INTEGER DEFAULT 0,
    failed_validations INTEGER DEFAULT 0,
    
    -- Security settings
    max_devices_per_key INTEGER DEFAULT 1,
    allow_vm_usage BOOLEAN DEFAULT FALSE,
    require_hwid_binding BOOLEAN DEFAULT TRUE
);

-- Add foreign key constraint if auth.users exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'users') THEN
        ALTER TABLE applications 
        ADD CONSTRAINT fk_applications_user_id 
        FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
        RAISE NOTICE '✓ Added foreign key constraint to auth.users';
    ELSE
        RAISE NOTICE '⚠ Skipped foreign key constraint - auth.users not found';
    END IF;
END $$;

-- =====================================================
-- STEP 4: ADD COLUMNS TO LICENSE_KEYS TABLE
-- =====================================================

-- Add app_id column
ALTER TABLE license_keys 
ADD COLUMN IF NOT EXISTS app_id UUID;

-- Add other new columns
ALTER TABLE license_keys 
ADD COLUMN IF NOT EXISTS tier VARCHAR(50) DEFAULT 'basic',
ADD COLUMN IF NOT EXISTS is_unlimited BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS bound_hardware_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS bound_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS notes TEXT,
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS is_revoked BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS revoked_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS revoked_reason TEXT;

-- Add user reference if it doesn't exist
ALTER TABLE license_keys 
ADD COLUMN IF NOT EXISTS created_by_user_id UUID;

-- Handle usage_count vs current_uses
DO $$ 
BEGIN
    -- If usage_count exists but current_uses doesn't, rename it
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'license_keys' AND column_name = 'usage_count') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'license_keys' AND column_name = 'current_uses') THEN
        ALTER TABLE license_keys RENAME COLUMN usage_count TO current_uses;
        RAISE NOTICE '✓ Renamed usage_count to current_uses';
    -- If neither exists, add current_uses
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'license_keys' AND column_name = 'current_uses') THEN
        ALTER TABLE license_keys ADD COLUMN current_uses INTEGER DEFAULT 0;
        RAISE NOTICE '✓ Added current_uses column';
    END IF;
END $$;

-- =====================================================
-- STEP 5: CREATE OTHER TABLES
-- =====================================================

-- License validation logs table
CREATE TABLE IF NOT EXISTS license_validation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    app_id UUID REFERENCES applications(id) ON DELETE CASCADE,
    license_key_id UUID REFERENCES license_keys(id) ON DELETE SET NULL,
    license_key VARCHAR(255) NOT NULL,
    hardware_id VARCHAR(255) NOT NULL,
    validation_result VARCHAR(50) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    error_message TEXT,
    security_flags JSONB,
    response_time_ms INTEGER
);

-- Hardware devices table
CREATE TABLE IF NOT EXISTS hardware_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    license_key_id UUID NOT NULL REFERENCES license_keys(id) ON DELETE CASCADE,
    hardware_id VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    os_info TEXT,
    cpu_info TEXT,
    mac_address VARCHAR(17),
    system_uuid VARCHAR(255),
    validation_count INTEGER DEFAULT 0,
    suspicious_activity_count INTEGER DEFAULT 0,
    is_flagged BOOLEAN DEFAULT FALSE,
    UNIQUE(license_key_id, hardware_id)
);

-- API usage statistics table
CREATE TABLE IF NOT EXISTS api_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    app_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    endpoint VARCHAR(100) NOT NULL,
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(app_id, date, endpoint)
);

-- =====================================================
-- STEP 6: CREATE DEFAULT APPLICATION
-- =====================================================

DO $$
DECLARE
    default_app_id UUID;
    admin_user_id UUID;
    app_id_value TEXT;
    app_secret_value TEXT;
BEGIN
    -- Generate credentials
    app_id_value := generate_app_id();
    app_secret_value := generate_app_secret();
    
    -- Try to get a user ID
    SELECT id INTO admin_user_id FROM auth.users LIMIT 1;
    
    -- If no auth.users, use a dummy UUID
    IF admin_user_id IS NULL THEN
        admin_user_id := gen_random_uuid();
        RAISE NOTICE '⚠ No users found in auth.users, using dummy user ID';
    END IF;
    
    -- Create default application
    INSERT INTO applications (
        user_id,
        app_id,
        app_secret,
        app_name,
        app_description,
        platform,
        discord_support
    ) VALUES (
        admin_user_id,
        app_id_value,
        app_secret_value,
        'Legacy Application',
        'Default application for existing license keys',
        'desktop',
        'discord.gg/server'
    ) RETURNING id INTO default_app_id;
    
    -- Link existing license keys to the default application
    UPDATE license_keys 
    SET app_id = default_app_id 
    WHERE app_id IS NULL;
    
    RAISE NOTICE '=== DEFAULT APPLICATION CREATED ===';
    RAISE NOTICE 'App ID: %', app_id_value;
    RAISE NOTICE 'App Secret: %', app_secret_value;
    RAISE NOTICE 'App Name: Legacy Application';
    RAISE NOTICE '';
    RAISE NOTICE 'Use these credentials in your .NET applications:';
    RAISE NOTICE 'AppId = "%"', app_id_value;
    RAISE NOTICE 'AppSecret = "%"', app_secret_value;
END $$;

-- =====================================================
-- STEP 7: CREATE INDEXES
-- =====================================================

-- Applications indexes
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications(user_id);
CREATE INDEX IF NOT EXISTS idx_applications_app_id ON applications(app_id);
CREATE INDEX IF NOT EXISTS idx_applications_app_secret ON applications(app_secret);

-- License keys indexes
CREATE INDEX IF NOT EXISTS idx_license_keys_app_id ON license_keys(app_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_license_key ON license_keys(license_key);
CREATE INDEX IF NOT EXISTS idx_license_keys_bound_hardware_id ON license_keys(bound_hardware_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_tier ON license_keys(tier);

-- Validation logs indexes
CREATE INDEX IF NOT EXISTS idx_validation_logs_app_id ON license_validation_logs(app_id);
CREATE INDEX IF NOT EXISTS idx_validation_logs_created_at ON license_validation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_validation_logs_hardware_id ON license_validation_logs(hardware_id);

-- =====================================================
-- STEP 8: CREATE TRIGGERS
-- =====================================================

-- Triggers for updated_at
DROP TRIGGER IF EXISTS update_applications_updated_at ON applications;
CREATE TRIGGER update_applications_updated_at 
    BEFORE UPDATE ON applications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

DO $$
DECLARE
    app_count INTEGER;
    key_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO app_count FROM applications;
    SELECT COUNT(*) INTO key_count FROM license_keys WHERE app_id IS NOT NULL;
    
    RAISE NOTICE '=== MIGRATION COMPLETED SUCCESSFULLY ===';
    RAISE NOTICE 'Applications created: %', app_count;
    RAISE NOTICE 'License keys migrated: %', key_count;
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Copy the App ID and App Secret from above';
    RAISE NOTICE '2. Update your .NET applications with the new credentials';
    RAISE NOTICE '3. Test license validation';
    RAISE NOTICE '';
    RAISE NOTICE 'To view your applications:';
    RAISE NOTICE 'SELECT app_id, app_secret, app_name FROM applications;';
END $$;
