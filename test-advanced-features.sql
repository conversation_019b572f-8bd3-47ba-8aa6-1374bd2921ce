-- Comprehensive Test Script for Advanced Blacklist Features
-- Run this in your Supabase SQL editor to test all advanced functionality

-- ============================================================================
-- 1. TEST IMPORT/EXPORT FUNCTIONALITY
-- ============================================================================

SELECT '=== Testing Import/Export Functions ===' as test_section;

-- Test creating an import job
SELECT 'Creating test import job...' as test_step;
SELECT create_import_job(
    'test-import.csv',
    1024,
    5,
    NULL,
    'skip',
    TRUE
) as import_job_id;

-- Get the job ID for further testing
DO $$
DECLARE
    test_job_id UUID;
    test_entries JSONB;
BEGIN
    -- Get the most recent import job
    SELECT id INTO test_job_id 
    FROM blacklist_import_jobs 
    ORDER BY created_at DESC 
    LIMIT 1;
    
    RAISE NOTICE 'Testing with job ID: %', test_job_id;
    
    -- Create test entries for bulk import
    test_entries := '[
        {"ip_address": "*************", "blacklist_reason": "Test IP 1"},
        {"hardware_id": "TEST-HWID-001", "blacklist_reason": "Test HWID 1"},
        {"region": "TEST", "blacklist_reason": "Test Region"},
        {"ip_address": "*************", "hardware_id": "TEST-HWID-002", "blacklist_reason": "Combined test"},
        {"invalid_entry": "should_fail", "blacklist_reason": "This should fail validation"}
    ]';
    
    -- Test bulk import
    PERFORM bulk_import_blacklist_entries(
        test_job_id,
        test_entries,
        'skip',
        TRUE
    );
    
    RAISE NOTICE 'Bulk import completed for job: %', test_job_id;
END $$;

-- Check import results
SELECT 'Import job results:' as test_step;
SELECT * FROM get_import_job_status((
    SELECT id FROM blacklist_import_jobs ORDER BY created_at DESC LIMIT 1
));

-- Check import errors
SELECT 'Import errors:' as test_step;
SELECT * FROM get_import_job_errors((
    SELECT id FROM blacklist_import_jobs ORDER BY created_at DESC LIMIT 1
), 10, 0);

-- Test export functionality
SELECT 'Testing export function...' as test_step;
SELECT export_data FROM export_blacklist_entries(
    'json',
    TRUE,  -- active only
    FALSE, -- not IP only
    FALSE, -- not HWID only
    FALSE, -- not region only
    NOW() - INTERVAL '1 hour', -- created after
    NOW() + INTERVAL '1 hour'  -- created before
);

-- ============================================================================
-- 2. TEST AUTOMATED RULES FUNCTIONALITY
-- ============================================================================

SELECT '=== Testing Automated Rules Functions ===' as test_section;

-- Test creating automated rules
SELECT 'Creating test automated rules...' as test_step;

-- Create IP failure rate rule
SELECT create_auto_rule(
    'Test IP Failure Rate',
    'Test rule for high IP failure rates',
    'ip_failure_rate',
    'ip',
    '{"failed_validations": 10, "failure_rate": 50, "timeframe_minutes": 60}',
    'blacklist',
    24, -- 24 hour blacklist
    'Automated: High failure rate detected - {rule_name}',
    TRUE, -- enabled
    100,  -- priority
    60,   -- cooldown minutes
    5,    -- max triggers per hour
    FALSE -- no admin approval required
) as ip_rule_id;

-- Create HWID license hopping rule
SELECT create_auto_rule(
    'Test HWID License Hopping',
    'Test rule for license key abuse',
    'hwid_license_hopping',
    'hwid',
    '{"unique_license_keys": 5, "timeframe_minutes": 60}',
    'blacklist',
    72, -- 72 hour blacklist
    'Automated: License abuse detected - {rule_name}',
    TRUE,
    90,
    120,
    3,
    FALSE
) as hwid_rule_id;

-- Create burst detection rule
SELECT create_auto_rule(
    'Test Burst Detection',
    'Test rule for rapid validation attempts',
    'burst_detection',
    'ip',
    '{"validation_frequency": 15, "timeframe_minutes": 5}',
    'blacklist',
    6, -- 6 hour blacklist
    'Automated: Burst activity detected - {rule_name}',
    TRUE,
    110,
    30,
    10,
    FALSE
) as burst_rule_id;

-- ============================================================================
-- 3. TEST PATTERN DETECTION
-- ============================================================================

SELECT '=== Testing Pattern Detection ===' as test_section;

-- Simulate validation patterns
SELECT 'Creating test validation patterns...' as test_step;

-- Create patterns for IP failure rate testing
DO $$
BEGIN
    -- Simulate high failure rate from IP
    FOR i IN 1..15 LOOP
        PERFORM update_validation_pattern(
            '**************'::inet,
            NULL,
            NULL,
            FALSE, -- failed validation
            gen_random_uuid(),
            gen_random_uuid()
        );
    END LOOP;
    
    -- Add some successful validations
    FOR i IN 1..3 LOOP
        PERFORM update_validation_pattern(
            '**************'::inet,
            NULL,
            NULL,
            TRUE, -- successful validation
            gen_random_uuid(),
            gen_random_uuid()
        );
    END LOOP;
    
    RAISE NOTICE 'Created failure rate pattern for IP **************';
END $$;

-- Create patterns for HWID license hopping
DO $$
BEGIN
    -- Simulate license hopping from HWID
    FOR i IN 1..8 LOOP
        PERFORM update_validation_pattern(
            NULL,
            'TEST-HWID-HOPPER',
            NULL,
            FALSE, -- failed validation
            gen_random_uuid(), -- different license key each time
            gen_random_uuid()
        );
    END LOOP;
    
    RAISE NOTICE 'Created license hopping pattern for HWID TEST-HWID-HOPPER';
END $$;

-- Create patterns for burst detection
DO $$
BEGIN
    -- Simulate burst activity
    FOR i IN 1..25 LOOP
        PERFORM update_validation_pattern(
            '*************0'::inet,
            'BURST-HWID',
            NULL,
            (i % 3 = 0), -- mix of success/failure
            gen_random_uuid(),
            gen_random_uuid()
        );
    END LOOP;
    
    RAISE NOTICE 'Created burst pattern for IP *************0';
END $$;

-- Check created patterns
SELECT 'Current validation patterns:' as test_step;
SELECT 
    pattern_type,
    ip_address,
    hardware_id,
    region,
    total_validations,
    failed_validations,
    failure_rate,
    validation_frequency,
    unique_license_keys
FROM validation_patterns
WHERE updated_at > NOW() - INTERVAL '10 minutes'
ORDER BY updated_at DESC;

-- ============================================================================
-- 4. TEST RULE EXECUTION
-- ============================================================================

SELECT '=== Testing Rule Execution ===' as test_section;

-- Test rule checking
SELECT 'Checking rules against patterns...' as test_step;
SELECT * FROM check_auto_rules(
    '**************'::inet,
    NULL,
    NULL
);

SELECT * FROM check_auto_rules(
    NULL,
    'TEST-HWID-HOPPER',
    NULL
);

SELECT * FROM check_auto_rules(
    '*************0'::inet,
    'BURST-HWID',
    NULL
);

-- ============================================================================
-- 5. TEST WHITELIST FUNCTIONALITY
-- ============================================================================

SELECT '=== Testing Whitelist Functions ===' as test_section;

-- Add whitelist entries
SELECT 'Adding whitelist entries...' as test_step;
INSERT INTO blacklist_whitelist (ip_address, whitelist_reason)
VALUES ('***********', 'Admin IP - always allow');

INSERT INTO blacklist_whitelist (hardware_id, whitelist_reason)
VALUES ('ADMIN-HWID-001', 'Admin device - always allow');

INSERT INTO blacklist_whitelist (region, whitelist_reason)
VALUES ('US', 'Trusted region');

-- Test whitelist checking
SELECT 'Testing whitelist checks...' as test_step;
SELECT is_whitelisted('***********'::inet, NULL, NULL) as ip_whitelisted;
SELECT is_whitelisted(NULL, 'ADMIN-HWID-001', NULL) as hwid_whitelisted;
SELECT is_whitelisted(NULL, NULL, 'US') as region_whitelisted;
SELECT is_whitelisted('**************'::inet, NULL, NULL) as not_whitelisted;

-- ============================================================================
-- 6. CLEANUP AND SUMMARY
-- ============================================================================

SELECT '=== Test Summary ===' as test_section;

-- Count created entries
SELECT 'Test data summary:' as test_step;
SELECT 
    (SELECT COUNT(*) FROM blacklist WHERE blacklist_reason LIKE '%Test%') as test_blacklist_entries,
    (SELECT COUNT(*) FROM blacklist_import_jobs WHERE filename LIKE '%test%') as test_import_jobs,
    (SELECT COUNT(*) FROM blacklist_auto_rules WHERE rule_name LIKE '%Test%') as test_auto_rules,
    (SELECT COUNT(*) FROM validation_patterns WHERE updated_at > NOW() - INTERVAL '10 minutes') as test_patterns,
    (SELECT COUNT(*) FROM blacklist_whitelist WHERE whitelist_reason LIKE '%Admin%' OR whitelist_reason LIKE '%Trusted%') as test_whitelist_entries;

-- Optional: Clean up test data (uncomment to clean up)
/*
DELETE FROM blacklist WHERE blacklist_reason LIKE '%Test%';
DELETE FROM blacklist_import_jobs WHERE filename LIKE '%test%';
DELETE FROM blacklist_auto_rules WHERE rule_name LIKE '%Test%';
DELETE FROM validation_patterns WHERE updated_at > NOW() - INTERVAL '10 minutes';
DELETE FROM blacklist_whitelist WHERE whitelist_reason LIKE '%Admin%' OR whitelist_reason LIKE '%Trusted%';
*/

SELECT 'Advanced features test completed successfully!' as result;
