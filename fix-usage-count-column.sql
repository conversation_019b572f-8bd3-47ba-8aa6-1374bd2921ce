-- =====================================================
-- Fix usage_count Column Reference Issue
-- =====================================================
-- This script fixes the get_license_key_stats function to use 
-- the correct column name (current_uses instead of usage_count)
-- Run this in your Supabase SQL Editor

-- Update get_license_key_stats function to use current_uses instead of usage_count
CREATE OR REPLACE FUNCTION get_license_key_stats()
RETURNS TABLE(
    total_keys INTEGER,
    active_keys INTEGER,
    expired_keys INTEGER,
    revoked_keys INTEGER,
    total_usage INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_active, TRUE) = TRUE AND COALESCE(is_revoked, FALSE) = FALSE)::INTEGER as active_keys,
        COUNT(*) FILTER (WHERE expires_at IS NOT NULL AND expires_at <= NOW())::INTEGER as expired_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_revoked, FALSE) = TRUE)::INTEGER as revoked_keys,
        COALESCE(SUM(current_uses), 0)::INTEGER as total_usage
    FROM license_keys;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update validate_license_key function to use current_uses instead of usage_count
CREATE OR REPLACE FUNCTION validate_license_key(
    key_to_validate TEXT,
    hardware_id TEXT DEFAULT NULL
)
RETURNS TABLE(
    valid BOOLEAN,
    message TEXT,
    key_data JSONB
) AS $$
DECLARE
    key_record RECORD;
    result_valid BOOLEAN := FALSE;
    result_message TEXT := 'Invalid license key';
    result_data JSONB := '{}'::jsonb;
BEGIN
    -- Get license key record
    SELECT * INTO key_record 
    FROM license_keys 
    WHERE license_key = key_to_validate;
    
    IF NOT FOUND THEN
        result_message := 'License key not found';
    ELSE
        -- Check if key is active
        IF COALESCE(key_record.is_revoked, FALSE) = TRUE THEN
            result_message := 'License key has been revoked';
        -- Check expiration
        ELSIF key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW() THEN
            result_message := 'License key has expired';
        -- Check if key is active
        ELSIF COALESCE(key_record.is_active, TRUE) = FALSE THEN
            result_message := 'License key is inactive';
        -- Check hardware binding
        ELSIF hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NOT NULL 
              AND key_record.bound_hardware_id != hardware_id THEN
            -- Revoke the key for security
            UPDATE license_keys 
            SET is_revoked = TRUE, 
                revoked_at = NOW(),
                revoked_reason = 'Hardware ID mismatch detected'
            WHERE id = key_record.id;
            
            result_message := 'SECURITY VIOLATION: License key has been revoked due to hardware ID mismatch. This indicates potential key sharing or unauthorized usage. Contact support at discord.gg/server if you believe this is an error.';
        -- Check usage limits
        ELSIF key_record.max_uses IS NOT NULL AND key_record.current_uses >= key_record.max_uses THEN
            result_message := 'License key usage limit exceeded';
        ELSE
            -- Key is valid
            result_valid := TRUE;
            result_message := 'License key validated successfully';
            
            -- Update usage statistics
            UPDATE license_keys 
            SET 
                current_uses = current_uses + 1,
                last_used_at = NOW(),
                bound_hardware_id = COALESCE(key_record.bound_hardware_id, hardware_id),
                bound_at = CASE 
                    WHEN key_record.bound_hardware_id IS NULL AND hardware_id IS NOT NULL 
                    THEN NOW() 
                    ELSE key_record.bound_at 
                END,
                updated_at = NOW()
            WHERE id = key_record.id;
            
            -- Prepare key data (excluding sensitive info)
            result_data := jsonb_build_object(
                'tier', key_record.tier,
                'expires_at', key_record.expires_at,
                'current_uses', key_record.current_uses + 1,
                'max_uses', key_record.max_uses,
                'is_unlimited', COALESCE(key_record.is_unlimited, FALSE)
            );
        END IF;
    END IF;
    
    RETURN QUERY SELECT result_valid, result_message, result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for the updated functions
GRANT EXECUTE ON FUNCTION get_license_key_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION validate_license_key(TEXT, TEXT) TO anon, authenticated;

-- Create function to create new applications
CREATE OR REPLACE FUNCTION create_application(
    p_user_id UUID,
    p_app_name VARCHAR(255),
    p_app_description TEXT DEFAULT NULL,
    p_platform VARCHAR(50) DEFAULT 'desktop',
    p_hwid_method VARCHAR(50) DEFAULT 'comprehensive',
    p_cache_duration_seconds INTEGER DEFAULT 3,
    p_discord_support VARCHAR(255) DEFAULT 'discord.gg/server',
    p_max_devices_per_key INTEGER DEFAULT 1,
    p_allow_vm_usage BOOLEAN DEFAULT FALSE,
    p_require_hwid_binding BOOLEAN DEFAULT TRUE
)
RETURNS TABLE(
    id UUID,
    app_id VARCHAR(50),
    app_secret VARCHAR(100),
    app_name VARCHAR(255),
    app_description TEXT,
    platform VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN
) AS $$
DECLARE
    new_app_id VARCHAR(50);
    new_app_secret VARCHAR(100);
    new_app_record RECORD;
BEGIN
    -- Generate unique app_id and app_secret
    new_app_id := generate_app_id();
    new_app_secret := generate_app_secret();

    -- Ensure app_id is unique
    WHILE EXISTS (SELECT 1 FROM applications WHERE applications.app_id = new_app_id) LOOP
        new_app_id := generate_app_id();
    END LOOP;

    -- Ensure app_secret is unique
    WHILE EXISTS (SELECT 1 FROM applications WHERE applications.app_secret = new_app_secret) LOOP
        new_app_secret := generate_app_secret();
    END LOOP;

    -- Insert the new application
    INSERT INTO applications (
        user_id,
        app_id,
        app_secret,
        app_name,
        app_description,
        platform,
        hwid_method,
        cache_duration_seconds,
        discord_support,
        max_devices_per_key,
        allow_vm_usage,
        require_hwid_binding,
        is_active
    ) VALUES (
        p_user_id,
        new_app_id,
        new_app_secret,
        p_app_name,
        p_app_description,
        p_platform,
        p_hwid_method,
        p_cache_duration_seconds,
        p_discord_support,
        p_max_devices_per_key,
        p_allow_vm_usage,
        p_require_hwid_binding,
        TRUE
    ) RETURNING * INTO new_app_record;

    -- Return the created application
    RETURN QUERY SELECT
        new_app_record.id,
        new_app_record.app_id,
        new_app_record.app_secret,
        new_app_record.app_name,
        new_app_record.app_description,
        new_app_record.platform,
        new_app_record.created_at,
        new_app_record.is_active;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION create_application(UUID, VARCHAR, TEXT, VARCHAR, VARCHAR, INTEGER, VARCHAR, INTEGER, BOOLEAN, BOOLEAN) TO authenticated;

-- Test the functions to make sure they work
SELECT * FROM get_license_key_stats();
