-- =====================================================
-- Ensure Data Consistency for Usage Counts
-- =====================================================
-- This script ensures all usage counts are consistent across tables
-- Run this after all other usage count fixes

-- Step 1: Verify and fix any null current_uses values
UPDATE license_keys 
SET current_uses = 0 
WHERE current_uses IS NULL;

-- Step 2: Ensure all license keys have proper default values
UPDATE license_keys 
SET 
    current_uses = COALESCE(current_uses, 0),
    is_active = COALESCE(is_active, TRUE),
    is_revoked = COALESCE(is_revoked, FALSE),
    is_unlimited = COALESCE(is_unlimited, FALSE)
WHERE current_uses IS NULL 
   OR is_active IS NULL 
   OR is_revoked IS NULL 
   OR is_unlimited IS NULL;

-- Step 3: Sync user_license_keys with license_keys current_uses
UPDATE user_license_keys 
SET total_uses = (
    SELECT COALESCE(lk.current_uses, 0)
    FROM license_keys lk 
    WHERE lk.id = user_license_keys.license_key_id
),
updated_at = NOW()
WHERE EXISTS (
    SELECT 1 FROM license_keys lk 
    WHERE lk.id = user_license_keys.license_key_id
    AND COALESCE(lk.current_uses, 0) != COALESCE(user_license_keys.total_uses, 0)
);

-- Step 4: Create comprehensive validation function
CREATE OR REPLACE FUNCTION validate_usage_count_consistency()
RETURNS TABLE(
    table_name TEXT,
    issue_description TEXT,
    affected_count BIGINT
) AS $$
BEGIN
    -- Check for license keys with null current_uses
    RETURN QUERY
    SELECT 
        'license_keys'::TEXT,
        'License keys with null current_uses'::TEXT,
        COUNT(*)::BIGINT
    FROM license_keys 
    WHERE current_uses IS NULL;
    
    -- Check for mismatched usage counts between tables
    RETURN QUERY
    SELECT 
        'user_license_keys'::TEXT,
        'Mismatched usage counts with license_keys'::TEXT,
        COUNT(*)::BIGINT
    FROM user_license_keys ulk
    JOIN license_keys lk ON ulk.license_key_id = lk.id
    WHERE COALESCE(ulk.total_uses, 0) != COALESCE(lk.current_uses, 0);
    
    -- Check for orphaned user_license_keys records
    RETURN QUERY
    SELECT 
        'user_license_keys'::TEXT,
        'Orphaned records (no matching license key)'::TEXT,
        COUNT(*)::BIGINT
    FROM user_license_keys ulk
    WHERE NOT EXISTS (
        SELECT 1 FROM license_keys lk WHERE lk.id = ulk.license_key_id
    );
    
    -- Check for hardware devices without user_license_keys records
    RETURN QUERY
    SELECT 
        'hardware_devices'::TEXT,
        'Devices without user_license_keys records'::TEXT,
        COUNT(*)::BIGINT
    FROM hardware_devices hd
    WHERE NOT EXISTS (
        SELECT 1 FROM user_license_keys ulk WHERE ulk.hardware_device_id = hd.id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Create function to fix all consistency issues
CREATE OR REPLACE FUNCTION fix_all_usage_consistency_issues()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
    fixed_count INTEGER;
BEGIN
    -- Fix null current_uses
    UPDATE license_keys SET current_uses = 0 WHERE current_uses IS NULL;
    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    result_text := result_text || 'Fixed ' || fixed_count || ' null current_uses values. ';
    
    -- Sync user_license_keys with license_keys
    UPDATE user_license_keys 
    SET total_uses = (
        SELECT COALESCE(lk.current_uses, 0)
        FROM license_keys lk 
        WHERE lk.id = user_license_keys.license_key_id
    ),
    updated_at = NOW()
    WHERE EXISTS (
        SELECT 1 FROM license_keys lk 
        WHERE lk.id = user_license_keys.license_key_id
        AND COALESCE(lk.current_uses, 0) != COALESCE(user_license_keys.total_uses, 0)
    );
    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    result_text := result_text || 'Synced ' || fixed_count || ' user_license_keys records. ';
    
    -- Remove orphaned user_license_keys records
    DELETE FROM user_license_keys 
    WHERE NOT EXISTS (
        SELECT 1 FROM license_keys lk WHERE lk.id = user_license_keys.license_key_id
    );
    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    result_text := result_text || 'Removed ' || fixed_count || ' orphaned user_license_keys records. ';
    
    -- Create missing user_license_keys records for hardware devices
    INSERT INTO user_license_keys (hardware_device_id, license_key_id, is_primary, total_uses, first_used_at, last_used_at)
    SELECT 
        hd.id,
        hd.license_key_id,
        TRUE,
        COALESCE(lk.current_uses, 0),
        hd.created_at,
        hd.updated_at
    FROM hardware_devices hd
    JOIN license_keys lk ON hd.license_key_id = lk.id
    WHERE NOT EXISTS (
        SELECT 1 FROM user_license_keys ulk WHERE ulk.hardware_device_id = hd.id
    )
    ON CONFLICT (hardware_device_id, license_key_id) DO NOTHING;
    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    result_text := result_text || 'Created ' || fixed_count || ' missing user_license_keys records.';
    
    RETURN result_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 6: Grant permissions
GRANT EXECUTE ON FUNCTION validate_usage_count_consistency() TO authenticated;
GRANT EXECUTE ON FUNCTION fix_all_usage_consistency_issues() TO authenticated;

-- Step 7: Run validation and fixes
SELECT * FROM validate_usage_count_consistency();
SELECT fix_all_usage_consistency_issues();

-- Step 8: Final validation
SELECT * FROM validate_usage_count_consistency();

-- Step 9: Show sample data to verify everything is working
SELECT 
    'license_keys' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE current_uses > 0) as records_with_usage,
    SUM(current_uses) as total_usage
FROM license_keys
UNION ALL
SELECT 
    'user_license_keys' as table_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE total_uses > 0) as records_with_usage,
    SUM(total_uses) as total_usage
FROM user_license_keys;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Data consistency verification and fixes completed!';
    RAISE NOTICE 'All usage counts should now be consistent across tables.';
    RAISE NOTICE 'Validation functions created for ongoing monitoring.';
END $$;
