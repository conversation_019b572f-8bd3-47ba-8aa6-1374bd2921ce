-- =====================================================
-- Test Usage Count Functionality
-- =====================================================
-- This script tests that usage counts are working correctly
-- Run this after applying all usage count fixes

-- Step 1: Create test data if needed
DO $$
DECLARE
    test_app_id UUID;
    test_license_id UUID;
    test_license_key TEXT := 'TEST-USAGE-COUNT-' || EXTRACT(EPOCH FROM NOW())::TEXT;
BEGIN
    -- Check if we have any applications to test with
    SELECT id INTO test_app_id FROM applications LIMIT 1;
    
    IF test_app_id IS NULL THEN
        RAISE NOTICE 'No applications found. Please create an application first to test usage counting.';
        RETURN;
    END IF;
    
    -- Create a test license key
    INSERT INTO license_keys (
        app_id,
        license_key,
        tier,
        max_uses,
        current_uses,
        is_active,
        is_revoked,
        created_at
    ) VALUES (
        test_app_id,
        test_license_key,
        'basic',
        10,
        0,
        TRUE,
        FALSE,
        NOW()
    ) RETURNING id INTO test_license_id;
    
    RAISE NOTICE 'Created test license key: % with ID: %', test_license_key, test_license_id;
END $$;

-- Step 2: Test the validate_license_key function
DO $$
DECLARE
    test_license_key TEXT;
    validation_result RECORD;
    initial_uses INTEGER;
    final_uses INTEGER;
BEGIN
    -- Get a test license key
    SELECT license_key, current_uses INTO test_license_key, initial_uses
    FROM license_keys 
    WHERE license_key LIKE 'TEST-USAGE-COUNT-%'
    ORDER BY created_at DESC 
    LIMIT 1;
    
    IF test_license_key IS NULL THEN
        RAISE NOTICE 'No test license key found. Please run the test data creation first.';
        RETURN;
    END IF;
    
    RAISE NOTICE 'Testing license key: % (initial uses: %)', test_license_key, initial_uses;
    
    -- Test validation (should increment usage)
    SELECT * INTO validation_result
    FROM validate_license_key(test_license_key, 'TEST-HWID-123');
    
    RAISE NOTICE 'Validation result: valid=%, message=%', validation_result.valid, validation_result.message;
    
    -- Check if usage was incremented
    SELECT current_uses INTO final_uses
    FROM license_keys 
    WHERE license_key = test_license_key;
    
    RAISE NOTICE 'Usage count after validation: % (expected: %)', final_uses, initial_uses + 1;
    
    IF final_uses = initial_uses + 1 THEN
        RAISE NOTICE '✅ Usage count increment test PASSED';
    ELSE
        RAISE NOTICE '❌ Usage count increment test FAILED';
    END IF;
END $$;

-- Step 3: Test data consistency between tables
CREATE OR REPLACE FUNCTION test_data_consistency()
RETURNS TABLE(
    test_name TEXT,
    status TEXT,
    details TEXT
) AS $$
DECLARE
    license_total BIGINT;
    user_license_total BIGINT;
    consistency_issues BIGINT;
BEGIN
    -- Test 1: Check total usage consistency
    SELECT SUM(COALESCE(current_uses, 0)) INTO license_total FROM license_keys;
    SELECT SUM(COALESCE(total_uses, 0)) INTO user_license_total FROM user_license_keys;
    
    RETURN QUERY SELECT 
        'Total Usage Consistency'::TEXT,
        CASE WHEN license_total = user_license_total THEN '✅ PASS' ELSE '❌ FAIL' END::TEXT,
        ('License keys total: ' || COALESCE(license_total::TEXT, '0') || 
         ', User license keys total: ' || COALESCE(user_license_total::TEXT, '0'))::TEXT;
    
    -- Test 2: Check for consistency issues
    SELECT COUNT(*) INTO consistency_issues
    FROM validate_usage_count_consistency()
    WHERE affected_count > 0;
    
    RETURN QUERY SELECT 
        'Data Consistency Issues'::TEXT,
        CASE WHEN consistency_issues = 0 THEN '✅ PASS' ELSE '❌ FAIL' END::TEXT,
        (consistency_issues::TEXT || ' consistency issues found')::TEXT;
    
    -- Test 3: Check consolidated_users view
    RETURN QUERY SELECT 
        'Consolidated Users View'::TEXT,
        CASE WHEN EXISTS(SELECT 1 FROM consolidated_users LIMIT 1) THEN '✅ PASS' ELSE '❌ FAIL' END::TEXT,
        'View is accessible and returns data'::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Run consistency tests
SELECT * FROM test_data_consistency();

-- Step 5: Test API-style validation
DO $$
DECLARE
    test_app_record RECORD;
    test_license_record RECORD;
    api_validation_result RECORD;
BEGIN
    -- Get test application and license
    SELECT * INTO test_app_record FROM applications LIMIT 1;
    SELECT * INTO test_license_record 
    FROM license_keys 
    WHERE app_id = test_app_record.id 
    AND is_active = TRUE 
    AND is_revoked = FALSE
    LIMIT 1;
    
    IF test_app_record IS NULL OR test_license_record IS NULL THEN
        RAISE NOTICE 'No suitable test data found for API validation test';
        RETURN;
    END IF;
    
    RAISE NOTICE 'Testing API-style validation for app: % license: %', 
                 test_app_record.app_name, test_license_record.license_key;
    
    -- Test the API validation function if it exists
    BEGIN
        SELECT * INTO api_validation_result
        FROM validate_license_key_api(
            test_app_record.id,
            test_license_record.license_key,
            'TEST-API-HWID-456',
            '192.168.1.100'::INET
        );
        
        RAISE NOTICE 'API validation result: valid=%, message=%', 
                     api_validation_result.valid, api_validation_result.message;
        
        IF api_validation_result.valid THEN
            RAISE NOTICE '✅ API validation test PASSED';
        ELSE
            RAISE NOTICE '❌ API validation test FAILED: %', api_validation_result.message;
        END IF;
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'ℹ️  API validation function not found - using standard validation';
    END;
END $$;

-- Step 6: Display current usage statistics
SELECT 
    'Current Usage Statistics' as report_section,
    COUNT(*) as total_license_keys,
    COUNT(*) FILTER (WHERE current_uses > 0) as keys_with_usage,
    SUM(COALESCE(current_uses, 0)) as total_usage_count,
    AVG(COALESCE(current_uses, 0)) as average_usage
FROM license_keys;

-- Step 7: Show sample license keys with usage
SELECT 
    license_key,
    tier,
    current_uses,
    max_uses,
    CASE 
        WHEN max_uses IS NULL THEN 'Unlimited'
        ELSE (current_uses::TEXT || '/' || max_uses::TEXT)
    END as usage_display,
    last_used_at
FROM license_keys 
WHERE current_uses > 0 OR license_key LIKE 'TEST-USAGE-COUNT-%'
ORDER BY current_uses DESC, created_at DESC
LIMIT 10;

-- Step 8: Show sample user usage data
SELECT 
    hardware_id,
    primary_license_key,
    primary_total_uses,
    total_license_keys_used,
    total_uses_across_all_keys,
    most_recent_activity
FROM consolidated_users 
WHERE primary_total_uses > 0 OR primary_license_key LIKE 'TEST-USAGE-COUNT-%'
ORDER BY primary_total_uses DESC, most_recent_activity DESC
LIMIT 10;

-- Step 9: Clean up test data
DO $$
BEGIN
    DELETE FROM license_keys WHERE license_key LIKE 'TEST-USAGE-COUNT-%';
    RAISE NOTICE 'Cleaned up test license keys';
END $$;

-- Final success message
DO $$
BEGIN
    RAISE NOTICE '🎉 Usage count functionality testing completed!';
    RAISE NOTICE 'Review the test results above to verify everything is working correctly.';
    RAISE NOTICE 'If all tests show ✅ PASS, your usage count tracking is working properly.';
END $$;
