-- Quick fix for users section - run this in Supabase SQL editor
-- This creates the minimum required components to get the users section working

-- Step 1: Create user_license_keys table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_license_keys (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    hardware_device_id UUID NOT NULL REFERENCES hardware_devices(id) ON DELETE CASCADE,
    license_key_id UUID NOT NULL REFERENCES license_keys(id) ON DELETE CASCADE,
    first_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    total_uses INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(hardware_device_id, license_key_id)
);

-- Step 2: <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_user_license_keys_hardware_device ON user_license_keys(hardware_device_id);
CREATE INDEX IF NOT EXISTS idx_user_license_keys_license_key ON user_license_keys(license_key_id);
CREATE INDEX IF NOT EXISTS idx_user_license_keys_primary ON user_license_keys(hardware_device_id, is_primary) WHERE is_primary = true;

-- Step 3: Clean up orphaned hardware devices
DELETE FROM hardware_devices 
WHERE license_key_id NOT IN (SELECT id FROM license_keys);

-- Step 4: Migrate existing data to user_license_keys table
INSERT INTO user_license_keys (hardware_device_id, license_key_id, is_primary, total_uses, first_used_at, last_used_at)
SELECT
    hd.id,
    hd.license_key_id,
    true, -- Set as primary since it's the only key for this user currently
    COALESCE(
        (SELECT COUNT(*) FROM license_validation_logs lvl
         WHERE lvl.hardware_device_id = hd.id AND lvl.success = true),
        0
    ) as total_uses,
    hd.created_at,
    hd.updated_at
FROM hardware_devices hd
INNER JOIN license_keys lk ON hd.license_key_id = lk.id  -- Ensure license key exists
ON CONFLICT (hardware_device_id, license_key_id) DO NOTHING;

-- Step 5: Drop and recreate consolidated_users view (full version)
DROP VIEW IF EXISTS consolidated_users;
CREATE VIEW consolidated_users AS
SELECT
    hd.id as hardware_device_id,
    hd.hardware_id,
    hd.ip_address,
    hd.is_banned,
    hd.created_at as first_used_at,
    hd.updated_at as last_used_at,

    -- Primary (most recent) license key info
    primary_lk.license_key as primary_license_key,
    primary_lk.tier as primary_tier,
    primary_ulk.last_used_at as primary_last_used_at,
    primary_ulk.total_uses as primary_total_uses,

    -- Aggregate data
    COUNT(ulk.id) as total_license_keys_used,
    SUM(ulk.total_uses) as total_uses_across_all_keys,
    MAX(ulk.last_used_at) as most_recent_activity,

    -- App info
    primary_lk.app_id

FROM hardware_devices hd
LEFT JOIN user_license_keys primary_ulk ON hd.id = primary_ulk.hardware_device_id AND primary_ulk.is_primary = true
LEFT JOIN license_keys primary_lk ON primary_ulk.license_key_id = primary_lk.id
LEFT JOIN user_license_keys ulk ON hd.id = ulk.hardware_device_id
GROUP BY
    hd.id, hd.hardware_id, hd.ip_address, hd.is_banned, hd.created_at, hd.updated_at,
    primary_lk.license_key, primary_lk.tier, primary_lk.app_id,
    primary_ulk.last_used_at, primary_ulk.total_uses;

-- Step 6: Create helper functions
CREATE OR REPLACE FUNCTION find_existing_user_by_ip_hwid(
    p_ip_address INET,
    p_hardware_id TEXT,
    p_app_id UUID
)
RETURNS UUID AS $$
DECLARE
    existing_device_id UUID;
BEGIN
    SELECT hd.id INTO existing_device_id
    FROM hardware_devices hd
    JOIN license_keys lk ON hd.license_key_id = lk.id
    WHERE hd.ip_address = p_ip_address
      AND hd.hardware_id = p_hardware_id
      AND lk.app_id = p_app_id
    LIMIT 1;

    RETURN existing_device_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION set_primary_license_key(
    p_hardware_device_id UUID,
    p_license_key_id UUID
)
RETURNS VOID AS $$
BEGIN
    -- Remove primary flag from all other keys for this user
    UPDATE user_license_keys
    SET is_primary = FALSE, updated_at = NOW()
    WHERE hardware_device_id = p_hardware_device_id;

    -- Set the specified key as primary and update hardware_devices table
    UPDATE user_license_keys
    SET is_primary = TRUE, last_used_at = NOW(), updated_at = NOW()
    WHERE hardware_device_id = p_hardware_device_id
      AND license_key_id = p_license_key_id;

    -- Update the hardware_devices table to point to the new primary key
    UPDATE hardware_devices
    SET license_key_id = p_license_key_id, updated_at = NOW()
    WHERE id = p_hardware_device_id;

    -- If no record exists in user_license_keys, create it and increment usage
    INSERT INTO user_license_keys (hardware_device_id, license_key_id, is_primary, total_uses)
    VALUES (p_hardware_device_id, p_license_key_id, TRUE, 1)
    ON CONFLICT (hardware_device_id, license_key_id)
    DO UPDATE SET
        is_primary = TRUE,
        last_used_at = NOW(),
        total_uses = user_license_keys.total_uses + 1,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_license_key_usage(
    p_hardware_device_id UUID,
    p_license_key_id UUID
)
RETURNS VOID AS $$
BEGIN
    -- Increment usage count in user_license_keys table
    UPDATE user_license_keys
    SET total_uses = total_uses + 1, last_used_at = NOW(), updated_at = NOW()
    WHERE hardware_device_id = p_hardware_device_id
      AND license_key_id = p_license_key_id;

    -- If no record exists, create it
    INSERT INTO user_license_keys (hardware_device_id, license_key_id, is_primary, total_uses)
    VALUES (p_hardware_device_id, p_license_key_id, FALSE, 1)
    ON CONFLICT (hardware_device_id, license_key_id)
    DO UPDATE SET
        total_uses = user_license_keys.total_uses + 1,
        last_used_at = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Step 6b: Fix existing usage counts (reset to accurate values based on validation logs)
UPDATE user_license_keys
SET total_uses = (
    SELECT COUNT(*)
    FROM license_validation_logs lvl
    WHERE lvl.hardware_device_id = user_license_keys.hardware_device_id
      AND lvl.license_key_id = user_license_keys.license_key_id
      AND lvl.success = true
);

-- Step 7: Grant permissions
GRANT SELECT ON user_license_keys TO authenticated;
GRANT SELECT ON consolidated_users TO authenticated;
GRANT EXECUTE ON FUNCTION find_existing_user_by_ip_hwid(INET, TEXT, UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION set_primary_license_key(UUID, UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION increment_license_key_usage(UUID, UUID) TO anon, authenticated;

-- Step 8: Enable RLS and create policies
ALTER TABLE user_license_keys ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own app user license keys" ON user_license_keys;
DROP POLICY IF EXISTS "System can insert user license keys" ON user_license_keys;
DROP POLICY IF EXISTS "System can update user license keys" ON user_license_keys;

-- Create RLS policies for user_license_keys
CREATE POLICY "Users can view their own app user license keys" ON user_license_keys
    FOR SELECT USING (
        hardware_device_id IN (
            SELECT hd.id FROM hardware_devices hd
            JOIN license_keys lk ON hd.license_key_id = lk.id
            JOIN applications a ON lk.app_id = a.id
            WHERE a.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert user license keys" ON user_license_keys
    FOR INSERT WITH CHECK (true);

CREATE POLICY "System can update user license keys" ON user_license_keys
    FOR UPDATE USING (true);
