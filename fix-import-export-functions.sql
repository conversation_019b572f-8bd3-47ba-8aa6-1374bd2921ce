-- Fix Import/Export Functions to Work with Updated Blacklist Schema
-- This updates the import functions to use the new application_id and user_id fields

-- Update the bulk_import_blacklist_entries function to include application_id and user_id
CREATE OR REPLACE FUNCTION bulk_import_blacklist_entries(
    job_id UUID,
    entries JSONB, -- Array of blacklist entry objects
    conflict_resolution VARCHAR(50) DEFAULT 'skip',
    validate_entries BOOLEAN DEFAULT TRUE,
    import_application_id UUID DEFAULT NULL,
    import_user_id UUID DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
    entry JSONB;
    entry_index INTEGER := 0;
    successful_count INTEGER := 0;
    failed_count INTEGER := 0;
    skipped_count INTEGER := 0;
    new_entry_id UUID;
    existing_entry RECORD;
    result JSONB;
BEGIN
    -- Update job status to processing
    PERFORM update_import_job_progress(job_id, 'processing');
    
    -- Process each entry
    FOR entry IN SELECT * FROM jsonb_array_elements(entries)
    LOOP
        entry_index := entry_index + 1;
        
        BEGIN
            -- Validate entry has at least one identifier
            IF validate_entries THEN
                IF (entry->>'ip_address' IS NULL OR entry->>'ip_address' = '') AND
                   (entry->>'hardware_id' IS NULL OR entry->>'hardware_id' = '') AND
                   (entry->>'region' IS NULL OR entry->>'region' = '') THEN
                    PERFORM add_import_error(
                        job_id,
                        entry_index,
                        entry,
                        'Entry must have at least one identifier (ip_address, hardware_id, or region)',
                        'validation'
                    );
                    failed_count := failed_count + 1;
                    CONTINUE;
                END IF;
            END IF;
            
            -- Check for existing entry (within the same application)
            SELECT * INTO existing_entry
            FROM blacklist
            WHERE (
                (NULLIF(entry->>'ip_address', '') IS NOT NULL AND ip_address = (entry->>'ip_address')::inet) OR
                (NULLIF(entry->>'hardware_id', '') IS NOT NULL AND hardware_id = entry->>'hardware_id') OR
                (NULLIF(entry->>'region', '') IS NOT NULL AND region = entry->>'region')
            )
            AND application_id = import_application_id
            AND is_active = TRUE
            LIMIT 1;
            
            IF FOUND THEN
                -- Handle conflict based on resolution strategy
                IF conflict_resolution = 'skip' THEN
                    skipped_count := skipped_count + 1;
                    CONTINUE;
                ELSIF conflict_resolution = 'error' THEN
                    PERFORM add_import_error(
                        job_id,
                        entry_index,
                        entry,
                        'Duplicate entry found',
                        'duplicate'
                    );
                    failed_count := failed_count + 1;
                    CONTINUE;
                ELSIF conflict_resolution = 'update' THEN
                    -- Update existing entry
                    UPDATE blacklist
                    SET
                        blacklist_reason = COALESCE(NULLIF(entry->>'blacklist_reason', ''), blacklist_reason),
                        expires_at = CASE 
                            WHEN entry->>'expires_at' IS NOT NULL AND entry->>'expires_at' != '' 
                            THEN (entry->>'expires_at')::timestamp with time zone 
                            ELSE expires_at 
                        END,
                        updated_at = NOW()
                    WHERE id = existing_entry.id;
                    successful_count := successful_count + 1;
                    CONTINUE;
                END IF;
            END IF;
            
            -- Create new entry using updated function signature
            SELECT add_blacklist_entry(
                NULLIF(entry->>'ip_address', '')::inet,
                NULLIF(entry->>'hardware_id', ''),
                NULLIF(entry->>'region', ''),
                COALESCE(NULLIF(entry->>'blacklist_reason', ''), 'Imported entry'),
                import_user_id, -- blocked_by_admin
                CASE 
                    WHEN entry->>'expires_at' IS NOT NULL AND entry->>'expires_at' != '' 
                    THEN (entry->>'expires_at')::timestamp with time zone 
                    ELSE NULL 
                END,
                import_application_id, -- entry_application_id
                import_user_id -- entry_user_id
            ) INTO new_entry_id;
            
            successful_count := successful_count + 1;
            
        EXCEPTION
            WHEN OTHERS THEN
                PERFORM add_import_error(
                    job_id,
                    entry_index,
                    entry,
                    SQLERRM,
                    'exception'
                );
                failed_count := failed_count + 1;
        END;
    END LOOP;
    
    -- Update job status and counts
    result := jsonb_build_object(
        'successful_count', successful_count,
        'failed_count', failed_count,
        'skipped_count', skipped_count,
        'total_count', entry_index
    );
    
    IF failed_count = 0 THEN
        PERFORM update_import_job_progress(job_id, 'completed', result);
    ELSE
        PERFORM update_import_job_progress(job_id, 'completed_with_errors', result);
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Update the export function to include application_id filtering
CREATE OR REPLACE FUNCTION export_blacklist_entries(
    export_format VARCHAR(10) DEFAULT 'json',
    active_only BOOLEAN DEFAULT TRUE,
    ip_only BOOLEAN DEFAULT FALSE,
    hwid_only BOOLEAN DEFAULT FALSE,
    region_only BOOLEAN DEFAULT FALSE,
    created_after TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    created_before TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    filter_application_id UUID DEFAULT NULL,
    filter_user_id UUID DEFAULT NULL
)
RETURNS TABLE(export_data TEXT) AS $$
DECLARE
    query_text TEXT;
    result_json JSONB;
    csv_header TEXT;
    csv_rows TEXT[];
    csv_row TEXT;
    entry RECORD;
BEGIN
    -- Build the base query with application filtering
    query_text := 'SELECT * FROM blacklist WHERE 1=1';
    
    -- Add application filter
    IF filter_application_id IS NOT NULL THEN
        query_text := query_text || ' AND application_id = ''' || filter_application_id || '''';
    END IF;
    
    -- Add user filter
    IF filter_user_id IS NOT NULL THEN
        query_text := query_text || ' AND user_id = ''' || filter_user_id || '''';
    END IF;
    
    -- Add active filter
    IF active_only THEN
        query_text := query_text || ' AND is_active = TRUE';
    END IF;
    
    -- Add type filters
    IF ip_only THEN
        query_text := query_text || ' AND ip_address IS NOT NULL';
    END IF;
    
    IF hwid_only THEN
        query_text := query_text || ' AND hardware_id IS NOT NULL';
    END IF;
    
    IF region_only THEN
        query_text := query_text || ' AND region IS NOT NULL';
    END IF;
    
    -- Add date filters
    IF created_after IS NOT NULL THEN
        query_text := query_text || ' AND created_at >= ''' || created_after || '''';
    END IF;
    
    IF created_before IS NOT NULL THEN
        query_text := query_text || ' AND created_at <= ''' || created_before || '''';
    END IF;
    
    query_text := query_text || ' ORDER BY created_at DESC';
    
    IF export_format = 'json' THEN
        -- Execute query and convert to JSON
        EXECUTE 'SELECT jsonb_agg(row_to_json(t)) FROM (' || query_text || ') t' INTO result_json;
        RETURN QUERY SELECT result_json::TEXT;
    ELSIF export_format = 'csv' THEN
        -- Generate CSV header
        csv_header := 'id,ip_address,hardware_id,region,blacklist_reason,blocked_by_admin_id,blocked_at,expires_at,is_active,lifted_at,lifted_by_admin_id,lift_reason,application_id,user_id,created_at,updated_at';
        csv_rows := ARRAY[csv_header];
        
        -- Execute query and convert to CSV
        FOR entry IN EXECUTE query_text LOOP
            csv_row := COALESCE(entry.id::TEXT, '') || ',' ||
                      COALESCE(entry.ip_address::TEXT, '') || ',' ||
                      COALESCE(entry.hardware_id, '') || ',' ||
                      COALESCE(entry.region, '') || ',' ||
                      COALESCE('"' || REPLACE(entry.blacklist_reason, '"', '""') || '"', '') || ',' ||
                      COALESCE(entry.blocked_by_admin_id::TEXT, '') || ',' ||
                      COALESCE(entry.blocked_at::TEXT, '') || ',' ||
                      COALESCE(entry.expires_at::TEXT, '') || ',' ||
                      COALESCE(entry.is_active::TEXT, '') || ',' ||
                      COALESCE(entry.lifted_at::TEXT, '') || ',' ||
                      COALESCE(entry.lifted_by_admin_id::TEXT, '') || ',' ||
                      COALESCE('"' || REPLACE(COALESCE(entry.lift_reason, ''), '"', '""') || '"', '') || ',' ||
                      COALESCE(entry.application_id::TEXT, '') || ',' ||
                      COALESCE(entry.user_id::TEXT, '') || ',' ||
                      COALESCE(entry.created_at::TEXT, '') || ',' ||
                      COALESCE(entry.updated_at::TEXT, '');
            csv_rows := array_append(csv_rows, csv_row);
        END LOOP;
        
        RETURN QUERY SELECT unnest(csv_rows);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Display completion message
SELECT 'Import/Export functions updated successfully! Now compatible with application_id and user_id fields.' as result;
