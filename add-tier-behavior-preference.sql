-- Migration to add tier_behavior_legacy column to user_preferences table
-- Run this script to add the new tier behavior preference

-- Step 1: Add the new tier_behavior_legacy column with default value FALSE (new behavior)
ALTER TABLE user_preferences
ADD COLUMN IF NOT EXISTS tier_behavior_legacy BOOLEAN DEFAULT FALSE;

-- Step 2: Update any existing records to have tier_behavior_legacy = FALSE (default to new behavior)
UPDATE user_preferences 
SET tier_behavior_legacy = FALSE 
WHERE tier_behavior_legacy IS NULL;

-- Step 3: Add comment to document the column
COMMENT ON COLUMN user_preferences.tier_behavior_legacy IS 'Controls tier behavior: false = new way (no forced defaults, allow no tier), true = old way (force first tier as default, require tier selection)';

-- Verification query (optional - run to check the migration worked)
-- SELECT user_id, tier_behavior_legacy FROM user_preferences LIMIT 5;
