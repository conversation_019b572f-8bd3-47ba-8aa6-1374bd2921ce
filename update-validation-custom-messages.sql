-- Update validate_license_key function to show custom revoked/suspended messages
-- Run this in Supabase SQL editor to enable custom messages for revoked/suspended keys

CREATE OR REPLACE FUNCTION validate_license_key(
    key_to_validate TEXT,
    hardware_id TEXT DEFAULT NULL
)
RETURNS TABLE(
    valid BOOLEAN,
    message TEXT,
    key_data JSONB
) AS $$
DECLARE
    key_record RECORD;
    global_ban_record RECORD;
    result_valid BOOLEAN := FALSE;
    result_message TEXT := 'Invalid license key';
    result_data JSONB := '{}'::jsonb;
    key_status_message TEXT := NULL;
    ban_message TEXT := NULL;
BEGIN
    -- Get license key record
    SELECT * INTO key_record
    FROM license_keys
    WHERE license_key = key_to_validate;

    IF NOT FOUND THEN
        result_message := 'License key not found';
    ELSE
        -- Check for global ban first if hardware_id is provided
        IF hardware_id IS NOT NULL THEN
            SELECT * INTO global_ban_record
            FROM is_globally_banned('127.0.0.1'::inet, hardware_id);

            IF global_ban_record.banned THEN
                ban_message := 'Access denied: Your IP/device combination has been banned. Reason: ' ||
                              COALESCE(global_ban_record.ban_reason, 'No reason provided') ||
                              '. Contact support at discord.gg/server for assistance.';
            END IF;
        END IF;

        -- Check key status issues
        IF COALESCE(key_record.is_revoked, FALSE) = TRUE THEN
            -- Use custom revoked reason if available
            key_status_message := COALESCE(key_record.revoked_reason, 'License key has been revoked');
        -- Check expiration (using current expires_at, not original)
        ELSIF key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW() THEN
            key_status_message := 'License key has expired';
        -- Check if key is active (suspended)
        ELSIF COALESCE(key_record.is_active, TRUE) = FALSE THEN
            -- Use custom suspended reason if available
            key_status_message := COALESCE(key_record.revoked_reason, 'License key has been suspended');
        -- Check usage limits (using current_uses)
        ELSIF key_record.max_uses IS NOT NULL AND key_record.current_uses >= key_record.max_uses THEN
            key_status_message := 'License key usage limit exceeded';
        -- Check hardware binding
        ELSIF hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NOT NULL
              AND key_record.bound_hardware_id != hardware_id THEN
            -- Revoke the key for security
            UPDATE license_keys
            SET is_revoked = TRUE,
                revoked_at = NOW(),
                revoked_reason = 'Hardware ID mismatch detected - potential key sharing violation',
                updated_at = NOW()
            WHERE id = key_record.id;

            key_status_message := 'SECURITY VIOLATION: License key has been revoked due to hardware ID mismatch. This indicates potential key sharing or unauthorized usage. Contact support at discord.gg/server if you believe this is an error.';
        END IF;

        -- Combine messages if both ban and key status exist
        IF ban_message IS NOT NULL AND key_status_message IS NOT NULL THEN
            result_message := ban_message || ' Additionally, this license key issue: ' || key_status_message || '.';
        ELSIF ban_message IS NOT NULL THEN
            result_message := ban_message;
        ELSIF key_status_message IS NOT NULL THEN
            result_message := key_status_message;
        ELSE
            -- Key is valid
            result_valid := TRUE;
            result_message := 'License key validated successfully';

            -- Update usage statistics
            UPDATE license_keys
            SET
                current_uses = current_uses + 1,
                last_used_at = NOW(),
                bound_hardware_id = COALESCE(key_record.bound_hardware_id, hardware_id),
                bound_at = CASE
                    WHEN key_record.bound_hardware_id IS NULL AND hardware_id IS NOT NULL
                    THEN NOW()
                    ELSE key_record.bound_at
                END,
                updated_at = NOW()
            WHERE id = key_record.id;

            -- Prepare key data (excluding sensitive info)
            result_data := jsonb_build_object(
                'tier', key_record.tier,
                'expires_at', key_record.expires_at,
                'current_uses', key_record.current_uses + 1,
                'max_uses', key_record.max_uses,
                'is_unlimited', COALESCE(key_record.is_unlimited, FALSE)
            );
        END IF;

        -- Prepare key data for failed validations too (for client status detection)
        IF NOT result_valid THEN
            result_data := jsonb_build_object(
                'tier', key_record.tier,
                'expires_at', key_record.expires_at,
                'current_uses', key_record.current_uses,
                'max_uses', key_record.max_uses,
                'is_unlimited', COALESCE(key_record.is_unlimited, FALSE),
                'is_revoked', COALESCE(key_record.is_revoked, FALSE),
                'is_suspended', NOT COALESCE(key_record.is_active, TRUE),
                'is_expired', (key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW())
            );
        END IF;
    END IF;

    RETURN QUERY SELECT result_valid, result_message, result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_license_key(TEXT, TEXT) TO anon, authenticated;

-- Test the updated function
DO $$
DECLARE
    test_result RECORD;
BEGIN
    RAISE NOTICE '=== Testing Custom Message Validation Function ===';
    
    -- Test with a non-existent key
    SELECT * INTO test_result FROM validate_license_key('TEST-FAKE-KEY-1234', 'TEST_HWID');
    RAISE NOTICE 'Non-existent key test - Valid: %, Message: %', test_result.valid, test_result.message;
    
    RAISE NOTICE 'Custom message validation function updated successfully.';
END $$;
