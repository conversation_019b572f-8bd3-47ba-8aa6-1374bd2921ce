-- Fix Analytics Data Flow
-- This script ensures that application statistics are properly updated from validation logs

-- =====================================================
-- STEP 1: CREATE FUNCTION TO UPDATE APPLICATION STATS
-- =====================================================

CREATE OR REPLACE FUNCTION update_application_stats(p_app_id UUID)
RETURNS VOID AS $$
DECLARE
    total_validations INTEGER;
    successful_validations INTEGER;
    failed_validations INTEGER;
BEGIN
    -- Get license key IDs for this application
    WITH app_license_keys AS (
        SELECT id FROM license_keys WHERE app_id = p_app_id
    )

    -- Count validations from logs
    SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN success = true THEN 1 END) as successful,
        COUNT(CASE WHEN success = false THEN 1 END) as failed
    INTO total_validations, successful_validations, failed_validations
    FROM license_validation_logs lvl
    WHERE lvl.license_key_id IN (SELECT id FROM app_license_keys);

    -- Update application statistics
    UPDATE applications
    SET
        total_validations = COALESCE(update_application_stats.total_validations, 0),
        successful_validations = COALESCE(update_application_stats.successful_validations, 0),
        failed_validations = COALESCE(update_application_stats.failed_validations, 0),
        updated_at = NOW()
    WHERE id = p_app_id;

END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 2: CREATE FUNCTION TO UPDATE ALL APPLICATION STATS
-- =====================================================

CREATE OR REPLACE FUNCTION update_all_application_stats()
RETURNS VOID AS $$
DECLARE
    app_record RECORD;
BEGIN
    -- Loop through all applications and update their stats
    FOR app_record IN SELECT id FROM applications LOOP
        PERFORM update_application_stats(app_record.id);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 3: CREATE TRIGGER TO AUTO-UPDATE STATS
-- =====================================================

-- Function to trigger stats update when validation log is inserted
CREATE OR REPLACE FUNCTION trigger_update_app_stats()
RETURNS TRIGGER AS $$
DECLARE
    target_app_id UUID;
BEGIN
    -- Get the application ID from the license key
    SELECT lk.app_id INTO target_app_id
    FROM license_keys lk
    WHERE lk.id = NEW.license_key_id;

    -- Update application stats if app_id found
    IF target_app_id IS NOT NULL THEN
        PERFORM update_application_stats(target_app_id);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on license_validation_logs
DROP TRIGGER IF EXISTS trigger_update_application_stats ON license_validation_logs;
CREATE TRIGGER trigger_update_application_stats
    AFTER INSERT ON license_validation_logs
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_app_stats();

-- =====================================================
-- STEP 4: CREATE REAL-TIME ANALYTICS FUNCTION
-- =====================================================

CREATE OR REPLACE FUNCTION get_application_analytics(
    p_app_id UUID,
    p_period VARCHAR DEFAULT '7d'
)
RETURNS JSON AS $$
DECLARE
    result JSON;
    start_date TIMESTAMP WITH TIME ZONE;
    total_validations INTEGER;
    successful_validations INTEGER;
    failed_validations INTEGER;
    success_rate DECIMAL;
    daily_stats JSON;
    error_breakdown JSON;
BEGIN
    -- Calculate start date based on period
    CASE p_period
        WHEN '24h' THEN start_date := NOW() - INTERVAL '24 hours';
        WHEN '7d' THEN start_date := NOW() - INTERVAL '7 days';
        WHEN '30d' THEN start_date := NOW() - INTERVAL '30 days';
        WHEN '90d' THEN start_date := NOW() - INTERVAL '90 days';
        ELSE start_date := NOW() - INTERVAL '7 days';
    END CASE;
    
    -- Get license key IDs for this application
    WITH app_license_keys AS (
        SELECT id FROM license_keys WHERE app_id = p_app_id
    ),
    
    -- Get validation logs for the period
    period_logs AS (
        SELECT * FROM license_validation_logs lvl
        WHERE lvl.license_key_id IN (SELECT id FROM app_license_keys)
        AND lvl.created_at >= start_date
    )
    
    -- Calculate basic stats
    SELECT 
        COUNT(*),
        COUNT(CASE WHEN success = true THEN 1 END),
        COUNT(CASE WHEN success = false THEN 1 END)
    INTO total_validations, successful_validations, failed_validations
    FROM period_logs;
    
    -- Calculate success rate
    success_rate := CASE 
        WHEN total_validations > 0 THEN 
            ROUND((successful_validations::DECIMAL / total_validations) * 100, 2)
        ELSE 0 
    END;
    
    -- Get daily stats
    WITH daily_data AS (
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as total,
            COUNT(CASE WHEN success = true THEN 1 END) as successful,
            COUNT(CASE WHEN success = false THEN 1 END) as failed
        FROM period_logs
        GROUP BY DATE(created_at)
        ORDER BY date
    )
    SELECT json_agg(
        json_build_object(
            'date', date,
            'total', total,
            'successful', successful,
            'failed', failed
        )
    ) INTO daily_stats
    FROM daily_data;
    
    -- Get error breakdown
    WITH error_data AS (
        SELECT 
            COALESCE(error_message, 'UNKNOWN_ERROR') as error_type,
            COUNT(*) as count
        FROM period_logs
        WHERE success = false
        GROUP BY error_message
    )
    SELECT json_object_agg(error_type, count) INTO error_breakdown
    FROM error_data;
    
    -- Build result JSON
    result := json_build_object(
        'period', p_period,
        'date_range', json_build_object(
            'start', start_date,
            'end', NOW()
        ),
        'validation_stats', json_build_object(
            'total_validations', COALESCE(total_validations, 0),
            'successful_validations', COALESCE(successful_validations, 0),
            'failed_validations', COALESCE(failed_validations, 0),
            'success_rate', COALESCE(success_rate, 0)
        ),
        'daily_stats', COALESCE(daily_stats, '[]'::json),
        'error_breakdown', COALESCE(error_breakdown, '{}'::json)
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 5: RUN INITIAL STATS UPDATE
-- =====================================================

-- Update all existing application stats
SELECT update_all_application_stats();

-- =====================================================
-- STEP 6: GRANT PERMISSIONS
-- =====================================================

GRANT EXECUTE ON FUNCTION update_application_stats(UUID) TO service_role, authenticated;
GRANT EXECUTE ON FUNCTION update_all_application_stats() TO service_role, authenticated;
GRANT EXECUTE ON FUNCTION get_application_analytics(UUID, VARCHAR) TO service_role, authenticated;
