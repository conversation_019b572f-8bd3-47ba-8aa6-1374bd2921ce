-- =====================================================
-- Simple Usage Count Fix (Works with any schema)
-- =====================================================
-- This script fixes usage count tracking without assuming existing columns
-- Run this in your Supabase SQL Editor

-- Step 1: Add current_uses column if it doesn't exist
ALTER TABLE license_keys 
ADD COLUMN IF NOT EXISTS current_uses INTEGER DEFAULT 0;

-- Step 2: Ensure all current_uses values are not null
UPDATE license_keys 
SET current_uses = 0 
WHERE current_uses IS NULL;

-- Step 3: Create or replace the validate_license_key function
CREATE OR REPLACE FUNCTION validate_license_key(
    key_to_validate TEXT,
    hardware_id TEXT DEFAULT NULL
)
RETURNS TABLE(
    valid BOOLEAN,
    message TEXT,
    key_data JSONB
) AS $$
DECLARE
    key_record RECORD;
    result_valid BOOLEAN := FALSE;
    result_message TEXT := 'Invalid license key';
    result_data JSONB := '{}'::jsonb;
BEGIN
    -- Get license key record
    SELECT * INTO key_record 
    FROM license_keys 
    WHERE license_key = key_to_validate;
    
    IF NOT FOUND THEN
        result_message := 'License key not found';
    ELSE
        -- Check if key is revoked
        IF COALESCE(key_record.is_revoked, FALSE) = TRUE THEN
            result_message := CASE 
                WHEN key_record.revoked_reason IS NOT NULL AND key_record.revoked_reason != '' 
                THEN key_record.revoked_reason || ' Contact support at discord.gg/server if you believe this is an error.'
                ELSE 'License key has been revoked. Contact support at discord.gg/server if you believe this is an error.'
            END;
        -- Check if key is suspended (inactive)
        ELSIF COALESCE(key_record.is_active, TRUE) = FALSE THEN
            result_message := 'License key has been suspended. Contact support at discord.gg/server if you believe this is an error.';
        -- Check expiration
        ELSIF key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW() THEN
            result_message := 'License key has expired. Contact support at discord.gg/server if you believe this is an error.';
        -- Check hardware binding
        ELSIF hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NOT NULL 
              AND key_record.bound_hardware_id != hardware_id THEN
            -- Revoke the key for security
            UPDATE license_keys 
            SET is_revoked = TRUE, 
                revoked_at = NOW(),
                revoked_reason = 'Hardware ID mismatch detected'
            WHERE id = key_record.id;
            
            result_message := 'SECURITY VIOLATION: License key has been revoked due to hardware ID mismatch. This indicates potential key sharing or unauthorized usage. Contact support at discord.gg/server if you believe this is an error.';
        -- Check usage limits
        ELSIF key_record.max_uses IS NOT NULL AND COALESCE(key_record.current_uses, 0) >= key_record.max_uses THEN
            result_message := 'License key usage limit exceeded. Contact support at discord.gg/server if you believe this is an error.';
        ELSE
            -- Key is valid
            result_valid := TRUE;
            result_message := 'License key validated successfully';
            
            -- Update usage statistics (increment current_uses)
            UPDATE license_keys
            SET current_uses = COALESCE(current_uses, 0) + 1,
                last_used_at = NOW(),
                bound_hardware_id = COALESCE(key_record.bound_hardware_id, hardware_id),
                bound_at = CASE 
                    WHEN key_record.bound_hardware_id IS NULL AND hardware_id IS NOT NULL 
                    THEN NOW() 
                    ELSE key_record.bound_at 
                END,
                updated_at = NOW()
            WHERE id = key_record.id;
            
            -- Build key data response (with incremented current_uses)
            result_data := jsonb_build_object(
                'tier', key_record.tier,
                'expires_at', key_record.expires_at,
                'current_uses', COALESCE(key_record.current_uses, 0) + 1,
                'max_uses', key_record.max_uses,
                'is_unlimited', (key_record.max_uses IS NULL),
                'is_expired', (key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW()),
                'is_usage_limit_reached', (key_record.max_uses IS NOT NULL AND COALESCE(key_record.current_uses, 0) + 1 >= key_record.max_uses),
                'bound_hardware_id', key_record.bound_hardware_id,
                'created_at', key_record.created_at
            );
        END IF;
    END IF;
    
    RETURN QUERY SELECT result_valid, result_message, result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Update get_license_key_stats function
CREATE OR REPLACE FUNCTION get_license_key_stats()
RETURNS TABLE(
    total_keys INTEGER,
    active_keys INTEGER,
    expired_keys INTEGER,
    revoked_keys INTEGER,
    total_usage INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_active, TRUE) = TRUE AND COALESCE(is_revoked, FALSE) = FALSE)::INTEGER as active_keys,
        COUNT(*) FILTER (WHERE expires_at IS NOT NULL AND expires_at <= NOW())::INTEGER as expired_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_revoked, FALSE) = TRUE)::INTEGER as revoked_keys,
        COALESCE(SUM(current_uses), 0)::INTEGER as total_usage
    FROM license_keys;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Grant necessary permissions
GRANT EXECUTE ON FUNCTION validate_license_key(TEXT, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_license_key_stats() TO authenticated;

-- Step 6: Add index on current_uses for better performance
CREATE INDEX IF NOT EXISTS idx_license_keys_current_uses ON license_keys(current_uses);

-- Step 7: Show current state of license keys
SELECT 
    COUNT(*) as total_license_keys,
    COUNT(*) FILTER (WHERE current_uses > 0) as keys_with_usage,
    SUM(COALESCE(current_uses, 0)) as total_current_uses,
    MAX(COALESCE(current_uses, 0)) as max_usage
FROM license_keys;

-- Step 8: Show sample license keys
SELECT 
    license_key,
    current_uses,
    max_uses,
    CASE 
        WHEN max_uses IS NULL THEN 'Unlimited'
        ELSE (COALESCE(current_uses, 0)::TEXT || '/' || max_uses::TEXT)
    END as usage_display,
    last_used_at,
    created_at
FROM license_keys 
ORDER BY created_at DESC
LIMIT 5;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Simple usage count fix completed successfully!';
    RAISE NOTICE 'The current_uses column has been added and the validation function updated.';
    RAISE NOTICE 'License keys should now properly track usage counts.';
END $$;
