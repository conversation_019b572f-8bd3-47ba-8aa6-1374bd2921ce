-- Fix Row Level Security policies to prevent authentication issues
-- This ensures API endpoints can access data while maintaining security

-- =====================================================
-- STEP 1: UPDATE RLS POLICIES FOR APPLICATIONS
-- =====================================================

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own applications" ON applications;
DROP POLICY IF EXISTS "Users can insert their own applications" ON applications;
DROP POLICY IF EXISTS "Users can update their own applications" ON applications;
DROP POLICY IF EXISTS "Users can delete their own applications" ON applications;

-- Create more flexible policies that work with service role
CREATE POLICY "Service role can access all applications" ON applications
    FOR ALL USING (
        -- Allow service role (API endpoints) full access
        auth.role() = 'service_role'
        OR
        -- Allow authenticated users to access their own applications
        (auth.role() = 'authenticated' AND auth.uid() = user_id)
    );

-- =====================================================
-- STEP 2: UPDATE RLS POLICIES FOR LICENSE KEYS
-- =====================================================

-- Drop existing license key policies if they exist
DROP POLICY IF EXISTS "Users can view license keys for their applications" ON license_keys;
DROP POLICY IF EXISTS "Users can insert license keys for their applications" ON license_keys;
DROP POLICY IF EXISTS "Users can update license keys for their applications" ON license_keys;
DROP POLICY IF EXISTS "Users can delete license keys for their applications" ON license_keys;

-- Create flexible license key policies
CREATE POLICY "Service role can access all license keys" ON license_keys
    FOR ALL USING (
        -- Allow service role (API endpoints) full access
        auth.role() = 'service_role'
        OR
        -- Allow authenticated users to access license keys for their applications
        (auth.role() = 'authenticated' AND EXISTS (
            SELECT 1 FROM applications 
            WHERE applications.id = license_keys.app_id 
            AND applications.user_id = auth.uid()
        ))
    );

-- =====================================================
-- STEP 3: ENSURE PROPER PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON applications TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON license_keys TO authenticated;

-- Grant permissions to anon role for API validation (limited)
GRANT SELECT ON applications TO anon;
GRANT SELECT, UPDATE ON license_keys TO anon;

-- =====================================================
-- STEP 4: CREATE HELPER FUNCTION FOR API VALIDATION
-- =====================================================

-- Function to validate app credentials (used by API endpoints)
CREATE OR REPLACE FUNCTION validate_app_credentials(
    p_app_id VARCHAR(50),
    p_app_secret VARCHAR(100)
)
RETURNS TABLE(
    app_uuid UUID,
    app_name VARCHAR(255),
    is_active BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id as app_uuid,
        a.app_name,
        a.is_active
    FROM applications a
    WHERE a.app_id = p_app_id 
    AND a.app_secret = p_app_secret;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION validate_app_credentials(VARCHAR(50), VARCHAR(100)) TO anon, authenticated, service_role;

-- =====================================================
-- STEP 5: VERIFICATION QUERIES
-- =====================================================

-- Test that policies work correctly
DO $$
BEGIN
    -- Test service role access
    RAISE NOTICE 'Testing RLS policies...';
    
    -- This should work for service role
    PERFORM COUNT(*) FROM applications;
    RAISE NOTICE '✓ Service role can access applications';
    
    PERFORM COUNT(*) FROM license_keys;
    RAISE NOTICE '✓ Service role can access license keys';
    
    RAISE NOTICE 'RLS policies updated successfully!';
END $$;
