-- Fix Blacklist Schema - Add Application and User Isolation
-- This migration adds the missing application_id and user_id fields to properly isolate blacklist entries

-- Step 1: Add the missing columns to the blacklist table
ALTER TABLE blacklist 
ADD COLUMN IF NOT EXISTS application_id UUID,
ADD COLUMN IF NOT EXISTS user_id UUID;

-- Step 2: Add foreign key constraints (assuming you have applications and users tables)
-- Note: Uncomment these if you have the referenced tables
-- ALTER TABLE blacklist 
-- ADD CONSTRAINT fk_blacklist_application 
-- FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE;

-- ALTER TABLE blacklist 
-- ADD CONSTRAINT fk_blacklist_user 
-- FOREI<PERSON>N KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 3: Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_blacklist_application_id ON blacklist(application_id);
CREATE INDEX IF NOT EXISTS idx_blacklist_user_id ON blacklist(user_id);
CREATE INDEX IF NOT EXISTS idx_blacklist_app_user ON blacklist(application_id, user_id);
CREATE INDEX IF NOT EXISTS idx_blacklist_app_active ON blacklist(application_id, is_active) WHERE is_active = TRUE;

-- Step 4: Update the add_blacklist_entry function to include application_id and user_id
CREATE OR REPLACE FUNCTION add_blacklist_entry(
    blacklist_ip_address INET DEFAULT NULL,
    blacklist_hardware_id TEXT DEFAULT NULL,
    blacklist_region TEXT DEFAULT NULL,
    blacklist_reason TEXT DEFAULT 'Blocked by administrator',
    blocked_by_admin UUID DEFAULT NULL,
    blacklist_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    entry_application_id UUID DEFAULT NULL,
    entry_user_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_entry_id UUID;
BEGIN
    -- Validate that at least one identifier is provided
    IF blacklist_ip_address IS NULL AND blacklist_hardware_id IS NULL AND blacklist_region IS NULL THEN
        RAISE EXCEPTION 'At least one identifier (IP address, Hardware ID, or Region) must be provided';
    END IF;
    
    -- Validate that application_id is provided
    IF entry_application_id IS NULL THEN
        RAISE EXCEPTION 'Application ID is required';
    END IF;
    
    -- Insert the new blacklist entry
    INSERT INTO blacklist (
        ip_address,
        hardware_id,
        region,
        blacklist_reason,
        blocked_by_admin_id,
        expires_at,
        application_id,
        user_id,
        is_active,
        blocked_at
    ) VALUES (
        blacklist_ip_address,
        blacklist_hardware_id,
        blacklist_region,
        blacklist_reason,
        blocked_by_admin,
        blacklist_expires_at,
        entry_application_id,
        entry_user_id,
        TRUE,
        NOW()
    )
    RETURNING id INTO new_entry_id;
    
    RETURN new_entry_id;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Update the is_blacklisted function to check within application scope
CREATE OR REPLACE FUNCTION is_blacklisted(
    check_ip_address INET DEFAULT NULL,
    check_hardware_id TEXT DEFAULT NULL,
    check_region TEXT DEFAULT NULL,
    check_application_id UUID DEFAULT NULL
)
RETURNS TABLE(
    blacklisted BOOLEAN,
    blacklist_reason TEXT,
    blocked_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    blacklist_type TEXT
) AS $$
DECLARE
    blacklist_entry RECORD;
    current_time TIMESTAMP WITH TIME ZONE := NOW();
BEGIN
    -- Look for matching blacklist entries within the application
    FOR blacklist_entry IN
        SELECT 
            b.blacklist_reason,
            b.blocked_at,
            b.expires_at,
            CASE 
                WHEN b.ip_address IS NOT NULL THEN 'IP'
                WHEN b.hardware_id IS NOT NULL THEN 'HWID'
                WHEN b.region IS NOT NULL THEN 'Region'
                ELSE 'Unknown'
            END as entry_type
        FROM blacklist b
        WHERE b.is_active = TRUE
          AND (b.application_id = check_application_id OR check_application_id IS NULL)
          AND (b.expires_at IS NULL OR b.expires_at > current_time)
          AND (
              (check_ip_address IS NOT NULL AND b.ip_address = check_ip_address) OR
              (check_ip_address IS NOT NULL AND b.ip_address IS NOT NULL AND check_ip_address << b.ip_address) OR
              (check_hardware_id IS NOT NULL AND b.hardware_id = check_hardware_id) OR
              (check_region IS NOT NULL AND b.region = check_region)
          )
        ORDER BY b.blocked_at DESC
        LIMIT 1
    LOOP
        -- Found a matching blacklist entry
        RETURN QUERY SELECT 
            TRUE,
            blacklist_entry.blacklist_reason,
            blacklist_entry.blocked_at,
            blacklist_entry.expires_at,
            blacklist_entry.entry_type;
        RETURN;
    END LOOP;
    
    -- No blacklist entry found
    RETURN QUERY SELECT 
        FALSE,
        NULL::TEXT,
        NULL::TIMESTAMP WITH TIME ZONE,
        NULL::TIMESTAMP WITH TIME ZONE,
        NULL::TEXT;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Update the lift_blacklist_entry function to include permission checks
CREATE OR REPLACE FUNCTION lift_blacklist_entry(
    blacklist_entry_id UUID,
    lifted_by_admin UUID DEFAULT NULL,
    lift_reason TEXT DEFAULT 'Lifted by administrator',
    check_application_id UUID DEFAULT NULL,
    check_user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    rows_affected INTEGER;
BEGIN
    -- Update the blacklist entry with permission checks
    UPDATE blacklist
    SET
        is_active = FALSE,
        lifted_at = NOW(),
        lifted_by_admin_id = lifted_by_admin,
        lift_reason = lift_reason,
        updated_at = NOW()
    WHERE id = blacklist_entry_id
      AND (check_application_id IS NULL OR application_id = check_application_id)
      AND (check_user_id IS NULL OR user_id = check_user_id)
      AND is_active = TRUE;

    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    RETURN rows_affected > 0;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create a function to get blacklist entries for a specific application/user
CREATE OR REPLACE FUNCTION get_blacklist_entries(
    filter_application_id UUID DEFAULT NULL,
    filter_user_id UUID DEFAULT NULL,
    filter_active_only BOOLEAN DEFAULT TRUE,
    filter_search TEXT DEFAULT NULL,
    filter_search_type TEXT DEFAULT 'all',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE(
    id UUID,
    ip_address INET,
    hardware_id TEXT,
    region TEXT,
    blacklist_reason TEXT,
    blocked_by_admin_id UUID,
    blocked_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN,
    lifted_at TIMESTAMP WITH TIME ZONE,
    lifted_by_admin_id UUID,
    lift_reason TEXT,
    application_id UUID,
    user_id UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        b.id,
        b.ip_address,
        b.hardware_id,
        b.region,
        b.blacklist_reason,
        b.blocked_by_admin_id,
        b.blocked_at,
        b.expires_at,
        b.is_active,
        b.lifted_at,
        b.lifted_by_admin_id,
        b.lift_reason,
        b.application_id,
        b.user_id,
        b.created_at,
        b.updated_at
    FROM blacklist b
    WHERE 
        (filter_application_id IS NULL OR b.application_id = filter_application_id)
        AND (filter_user_id IS NULL OR b.user_id = filter_user_id)
        AND (NOT filter_active_only OR b.is_active = TRUE)
        AND (
            filter_search IS NULL OR
            (filter_search_type = 'ip' AND b.ip_address::text ILIKE '%' || filter_search || '%') OR
            (filter_search_type = 'hwid' AND b.hardware_id ILIKE '%' || filter_search || '%') OR
            (filter_search_type = 'region' AND b.region ILIKE '%' || filter_search || '%') OR
            (filter_search_type = 'all' AND (
                b.ip_address::text ILIKE '%' || filter_search || '%' OR
                b.hardware_id ILIKE '%' || filter_search || '%' OR
                b.region ILIKE '%' || filter_search || '%' OR
                b.blacklist_reason ILIKE '%' || filter_search || '%'
            ))
        )
    ORDER BY b.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Add a constraint to ensure application_id is always provided for new entries
-- (We'll make this optional for now to avoid breaking existing data)
-- ALTER TABLE blacklist 
-- ADD CONSTRAINT blacklist_requires_application 
-- CHECK (application_id IS NOT NULL);

COMMENT ON TABLE blacklist IS 'Blacklist entries are now scoped to specific applications and users for proper isolation';
COMMENT ON COLUMN blacklist.application_id IS 'ID of the application this blacklist entry belongs to';
COMMENT ON COLUMN blacklist.user_id IS 'ID of the user who owns this blacklist entry';

-- Display completion message
SELECT 'Blacklist schema updated successfully! Application and user isolation added.' as result;
