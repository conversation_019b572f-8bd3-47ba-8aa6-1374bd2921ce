-- Reset all usage counts to fix double counting issue
-- Run this in Supabase SQL editor

-- Step 1: Reset all usage counts in user_license_keys to 0
UPDATE user_license_keys SET total_uses = 0;

-- Step 2: Reset all usage counts in license_keys to 0  
UPDATE license_keys SET current_uses = 0;

-- Step 3: Delete all existing validation logs to start fresh
-- (Optional - only if you want to completely reset usage tracking)
-- DELETE FROM license_validation_logs;

-- Step 4: Update the increment function to be simpler and avoid conflicts
CREATE OR REPLACE FUNCTION increment_license_key_usage(
    p_hardware_device_id UUID,
    p_license_key_id UUID
)
RETURNS VOID AS $$
BEGIN
    -- Simply increment usage count in user_license_keys table
    UPDATE user_license_keys 
    SET total_uses = total_uses + 1, 
        last_used_at = NOW(), 
        updated_at = NOW()
    WHERE hardware_device_id = p_hardware_device_id 
      AND license_key_id = p_license_key_id;
      
    -- If no record exists, create it with usage = 1
    IF NOT FOUND THEN
        INSERT INTO user_license_keys (hardware_device_id, license_key_id, is_primary, total_uses, first_used_at, last_used_at)
        VALUES (p_hardware_device_id, p_license_key_id, FALSE, 1, NOW(), NOW())
        ON CONFLICT (hardware_device_id, license_key_id) 
        DO UPDATE SET 
            total_uses = user_license_keys.total_uses + 1,
            last_used_at = NOW(),
            updated_at = NOW();
    END IF;
END;
$$ LANGUAGE plpgsql;
