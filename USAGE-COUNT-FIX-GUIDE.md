# Usage Count Tracking Fix Guide

This guide provides step-by-step instructions to fix all usage count tracking issues in the KeyAuth admin dashboard.

## Problem Summary

The usage counts were showing as 0 in both the license key management section and users management section due to:

1. **Column Name Inconsistency**: Original schema used `usage_count` but newer functions expected `current_uses`
2. **Missing Column**: The `current_uses` column didn't exist in some databases
3. **Inconsistent Functions**: Multiple versions of validation functions using different column names
4. **Data Synchronization**: User tables not synchronized with license key usage data
5. **API Issues**: Validation endpoint not properly incrementing usage counts

## Fix Steps

### Step 1: Run the Comprehensive Usage Count Fix

Copy and paste this script into your Supabase SQL Editor:

```sql
-- File: database-migration/fix-usage-count-tracking-comprehensive.sql
```

This script will:
- Add the `current_uses` column if it doesn't exist
- Migrate data from `usage_count` to `current_uses`
- Update the `validate_license_key` function to use `current_uses`
- Update the `get_license_key_stats` function
- Synchronize user license keys with license key usage

### Step 2: Fix Users Section Usage Display

Copy and paste this script into your Supabase SQL Editor:

```sql
-- File: database-migration/fix-users-usage-display.sql
```

This script will:
- Update the `consolidated_users` view to use accurate usage data
- Create automatic synchronization triggers
- Add functions for accurate user usage statistics

### Step 3: Ensure Data Consistency

Copy and paste this script into your Supabase SQL Editor:

```sql
-- File: database-migration/ensure-data-consistency.sql
```

This script will:
- Verify and fix any data inconsistencies
- Create validation functions for ongoing monitoring
- Ensure all tables are properly synchronized

### Step 4: Verify the Fix

After running all scripts, verify the fix by:

1. **Check License Key Usage in Admin Dashboard**:
   - Go to your application's license keys section
   - Verify that usage counts are displayed correctly (not showing 0)
   - Test a license key validation to see if the count increments

2. **Check Users Section**:
   - Go to your application's users section
   - Verify that user usage statistics are displayed correctly
   - Check that total usage counts match the license key usage

3. **Test License Key Validation**:
   - Use a license key validation (via API or SDK)
   - Check that the usage count increments in both sections
   - Verify that the validation response includes correct usage data

## Verification Queries

Run these queries in Supabase SQL Editor to verify everything is working:

```sql
-- Check license key usage counts
SELECT 
    license_key,
    current_uses,
    max_uses,
    last_used_at
FROM license_keys 
WHERE current_uses > 0
ORDER BY current_uses DESC
LIMIT 10;

-- Check user usage statistics
SELECT 
    hardware_id,
    primary_license_key,
    primary_total_uses,
    total_uses_across_all_keys
FROM consolidated_users 
WHERE primary_total_uses > 0
ORDER BY primary_total_uses DESC
LIMIT 10;

-- Validate data consistency
SELECT * FROM validate_usage_count_consistency();
```

## Expected Results

After applying all fixes:

1. **License Key Section**: Should display actual usage counts (e.g., "3/10" or "5/∞")
2. **Users Section**: Should show accurate user usage statistics
3. **API Validation**: Should properly increment usage counts
4. **Data Consistency**: All tables should have synchronized usage data

## Troubleshooting

### If usage counts are still showing as 0:

1. **Check Column Existence**:
   ```sql
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_name = 'license_keys' 
   AND column_name IN ('current_uses', 'usage_count');
   ```

2. **Check Function Version**:
   ```sql
   SELECT routine_name, routine_definition 
   FROM information_schema.routines 
   WHERE routine_name = 'validate_license_key';
   ```

3. **Manual Data Sync**:
   ```sql
   SELECT sync_usage_counts();
   SELECT fix_all_usage_consistency_issues();
   ```

### If API validation isn't incrementing counts:

1. Check that the API endpoint is using the updated validation logic
2. Verify that the `current_uses` column is being updated
3. Check the validation logs for any errors

## Maintenance

To keep usage counts accurate:

1. **Regular Consistency Checks**:
   ```sql
   SELECT * FROM validate_usage_count_consistency();
   ```

2. **Manual Sync if Needed**:
   ```sql
   SELECT fix_all_usage_consistency_issues();
   ```

3. **Monitor Validation Logs**:
   ```sql
   SELECT * FROM license_validation_logs 
   WHERE created_at > NOW() - INTERVAL '1 day'
   ORDER BY created_at DESC;
   ```

## Files Created

- `fix-usage-count-tracking-comprehensive.sql` - Main fix for usage tracking
- `fix-users-usage-display.sql` - Fix for users section display
- `ensure-data-consistency.sql` - Data consistency verification and fixes
- `USAGE-COUNT-FIX-GUIDE.md` - This guide

## Support

If you encounter any issues after applying these fixes, check:

1. Supabase SQL Editor for any error messages
2. Browser console for API errors
3. License validation logs for validation issues

The fixes include comprehensive error handling and should resolve all usage count tracking issues in the KeyAuth system.
