-- Add user settings table for dashboard preferences
-- Run this in Supabase SQL editor to add user settings functionality

-- Create user_settings table
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Language Settings (Appearance)
    language VARCHAR(10) DEFAULT 'en', -- 'en', 'es', 'fr', 'de', 'pt', 'ru', 'zh', 'ja'

    -- Notification Settings
    email_notifications BOOLEAN DEFAULT true,
    security_alerts BOOLEAN DEFAULT true,
    marketing_emails BOOLEAN DEFAULT false,
    weekly_reports BOOLEAN DEFAULT true,

    -- Security Settings
    session_timeout_minutes INTEGER DEFAULT 60,
    require_2fa BOOLEAN DEFAULT false,
    auto_logout_inactive BOOLEAN DEFAULT true,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique index on user_id (one settings record per user)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_settings_updated_at ON user_settings(updated_at);

-- Enable RLS
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view their own settings" ON user_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings" ON user_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings" ON user_settings
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own settings" ON user_settings
    FOR DELETE USING (auth.uid() = user_id);

-- Function to get or create user settings
CREATE OR REPLACE FUNCTION get_or_create_user_settings(p_user_id UUID)
RETURNS user_settings AS $$
DECLARE
    settings_record user_settings;
BEGIN
    -- Try to get existing settings
    SELECT * INTO settings_record
    FROM user_settings
    WHERE user_id = p_user_id;
    
    -- If no settings exist, create default ones
    IF NOT FOUND THEN
        INSERT INTO user_settings (user_id)
        VALUES (p_user_id)
        RETURNING * INTO settings_record;
    END IF;
    
    RETURN settings_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user settings (simplified approach)
CREATE OR REPLACE FUNCTION update_user_settings(
    p_user_id UUID,
    p_settings JSONB
)
RETURNS user_settings AS $$
DECLARE
    settings_record user_settings;
    update_query TEXT;
    update_parts TEXT[] := ARRAY[]::TEXT[];
    setting_key TEXT;
    setting_value JSONB;
BEGIN
    -- Ensure settings record exists
    PERFORM get_or_create_user_settings(p_user_id);

    -- Build update parts dynamically
    FOR setting_key, setting_value IN SELECT * FROM jsonb_each(p_settings)
    LOOP
        CASE setting_key
            WHEN 'language' THEN
                update_parts := array_append(update_parts, 'language = ' || quote_literal(setting_value #>> '{}'));
            WHEN 'email_notifications' THEN
                update_parts := array_append(update_parts, 'email_notifications = ' || (setting_value #>> '{}')::BOOLEAN);
            WHEN 'security_alerts' THEN
                update_parts := array_append(update_parts, 'security_alerts = ' || (setting_value #>> '{}')::BOOLEAN);
            WHEN 'marketing_emails' THEN
                update_parts := array_append(update_parts, 'marketing_emails = ' || (setting_value #>> '{}')::BOOLEAN);
            WHEN 'weekly_reports' THEN
                update_parts := array_append(update_parts, 'weekly_reports = ' || (setting_value #>> '{}')::BOOLEAN);
            WHEN 'session_timeout_minutes' THEN
                update_parts := array_append(update_parts, 'session_timeout_minutes = ' || (setting_value #>> '{}')::INTEGER);
            WHEN 'require_2fa' THEN
                update_parts := array_append(update_parts, 'require_2fa = ' || (setting_value #>> '{}')::BOOLEAN);
            WHEN 'auto_logout_inactive' THEN
                update_parts := array_append(update_parts, 'auto_logout_inactive = ' || (setting_value #>> '{}')::BOOLEAN);
        END CASE;
    END LOOP;

    -- Execute update if we have parts to update
    IF array_length(update_parts, 1) > 0 THEN
        update_query := 'UPDATE user_settings SET ' || array_to_string(update_parts, ', ') || ', updated_at = NOW() WHERE user_id = ' || quote_literal(p_user_id);
        EXECUTE update_query;
    END IF;

    -- Return updated settings
    SELECT * INTO settings_record
    FROM user_settings
    WHERE user_id = p_user_id;

    RETURN settings_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_or_create_user_settings(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION update_user_settings(UUID, JSONB) TO anon, authenticated;

-- Insert default settings for existing users (optional)
INSERT INTO user_settings (user_id)
SELECT id FROM auth.users
WHERE id NOT IN (SELECT user_id FROM user_settings)
ON CONFLICT (user_id) DO NOTHING;
