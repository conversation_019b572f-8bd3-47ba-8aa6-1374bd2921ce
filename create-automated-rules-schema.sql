-- Automated Blacklist Rules Schema
-- This creates tables and functions for automated pattern detection and rule-based blacklisting

-- Create automated rules table
CREATE TABLE IF NOT EXISTS blacklist_auto_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Rule identification
    rule_name VARCHAR(255) NOT NULL,
    rule_description TEXT,
    
    -- Rule configuration
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('ip_failure_rate', 'hwid_license_hopping', 'geographic_anomaly', 'burst_detection', 'custom')),
    target_type VARCHAR(50) NOT NULL CHECK (target_type IN ('ip', 'hwid', 'region', 'combined')),
    
    -- Trigger conditions (JSONB for flexibility)
    trigger_conditions JSONB NOT NULL,
    -- Example: {"failed_validations": 50, "timeframe_minutes": 60, "threshold_type": "greater_than"}
    
    -- Action configuration
    action_type VARCHAR(50) DEFAULT 'blacklist' CHECK (action_type IN ('blacklist', 'alert', 'review')),
    blacklist_duration_hours INTEGER, -- NULL = permanent
    blacklist_reason_template TEXT DEFAULT 'Automated: Rule triggered - {rule_name}',
    
    -- Rule status and settings
    is_enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 100, -- Lower number = higher priority
    cooldown_minutes INTEGER DEFAULT 60, -- Prevent rapid re-triggering
    
    -- Safety settings
    max_triggers_per_hour INTEGER DEFAULT 10,
    require_admin_approval BOOLEAN DEFAULT FALSE,
    whitelist_override BOOLEAN DEFAULT TRUE, -- Respect whitelist entries
    
    -- Admin tracking
    created_by_admin_id UUID,
    last_modified_by_admin_id UUID,
    
    -- Statistics
    total_triggers INTEGER DEFAULT 0,
    last_triggered_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create rule execution logs table
CREATE TABLE IF NOT EXISTS blacklist_rule_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    rule_id UUID NOT NULL REFERENCES blacklist_auto_rules(id) ON DELETE CASCADE,
    
    -- Execution details
    trigger_data JSONB NOT NULL, -- Data that triggered the rule
    matched_conditions JSONB NOT NULL, -- Which conditions were met
    
    -- Target information
    target_ip INET,
    target_hwid TEXT,
    target_region TEXT,
    
    -- Action taken
    action_taken VARCHAR(50) NOT NULL,
    blacklist_entry_id UUID, -- Reference to created blacklist entry
    requires_approval BOOLEAN DEFAULT FALSE,
    approved_by_admin_id UUID,
    approved_at TIMESTAMP WITH TIME ZONE,
    
    -- Execution result
    execution_status VARCHAR(50) DEFAULT 'pending' CHECK (execution_status IN ('pending', 'executed', 'failed', 'cancelled', 'approved', 'rejected')),
    error_message TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pattern detection data table (for tracking validation patterns)
CREATE TABLE IF NOT EXISTS validation_patterns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Pattern identification
    pattern_key VARCHAR(255) NOT NULL, -- e.g., "ip:*************", "hwid:ABC123"
    pattern_type VARCHAR(50) NOT NULL CHECK (pattern_type IN ('ip', 'hwid', 'region', 'ip_hwid')),
    
    -- Pattern data
    ip_address INET,
    hardware_id TEXT,
    region TEXT,
    
    -- Metrics (sliding window data)
    total_validations INTEGER DEFAULT 0,
    failed_validations INTEGER DEFAULT 0,
    unique_license_keys INTEGER DEFAULT 0,
    unique_applications INTEGER DEFAULT 0,
    
    -- Time windows
    last_validation_at TIMESTAMP WITH TIME ZONE,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    window_end TIMESTAMP WITH TIME ZONE,
    
    -- Calculated metrics
    failure_rate DECIMAL(5,2) DEFAULT 0.0,
    validation_frequency DECIMAL(10,2) DEFAULT 0.0, -- validations per minute
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint for pattern tracking
    UNIQUE(pattern_key, window_start)
);

-- Create whitelist table for automated rule overrides
CREATE TABLE IF NOT EXISTS blacklist_whitelist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Whitelist identifiers
    ip_address INET,
    hardware_id TEXT,
    region TEXT,
    
    -- Whitelist metadata
    whitelist_reason TEXT NOT NULL,
    whitelisted_by_admin_id UUID,
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure at least one identifier
    CONSTRAINT whitelist_has_identifier CHECK (
        ip_address IS NOT NULL OR 
        hardware_id IS NOT NULL OR 
        region IS NOT NULL
    )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_auto_rules_enabled ON blacklist_auto_rules(is_enabled) WHERE is_enabled = TRUE;
CREATE INDEX IF NOT EXISTS idx_auto_rules_type ON blacklist_auto_rules(rule_type);
CREATE INDEX IF NOT EXISTS idx_rule_executions_rule_id ON blacklist_rule_executions(rule_id);
CREATE INDEX IF NOT EXISTS idx_rule_executions_status ON blacklist_rule_executions(execution_status);
CREATE INDEX IF NOT EXISTS idx_rule_executions_created ON blacklist_rule_executions(created_at);
CREATE INDEX IF NOT EXISTS idx_validation_patterns_key ON validation_patterns(pattern_key);
CREATE INDEX IF NOT EXISTS idx_validation_patterns_type ON validation_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_validation_patterns_updated ON validation_patterns(updated_at);
CREATE INDEX IF NOT EXISTS idx_whitelist_ip ON blacklist_whitelist(ip_address) WHERE ip_address IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_whitelist_hwid ON blacklist_whitelist(hardware_id) WHERE hardware_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_whitelist_region ON blacklist_whitelist(region) WHERE region IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_whitelist_active ON blacklist_whitelist(is_active) WHERE is_active = TRUE;

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_auto_rules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_auto_rules_updated_at ON blacklist_auto_rules;
CREATE TRIGGER trigger_update_auto_rules_updated_at
    BEFORE UPDATE ON blacklist_auto_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_rules_updated_at();

DROP TRIGGER IF EXISTS trigger_update_validation_patterns_updated_at ON validation_patterns;
CREATE TRIGGER trigger_update_validation_patterns_updated_at
    BEFORE UPDATE ON validation_patterns
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_rules_updated_at();

DROP TRIGGER IF EXISTS trigger_update_whitelist_updated_at ON blacklist_whitelist;
CREATE TRIGGER trigger_update_whitelist_updated_at
    BEFORE UPDATE ON blacklist_whitelist
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_rules_updated_at();

-- Function to check if target is whitelisted
CREATE OR REPLACE FUNCTION is_whitelisted(
    check_ip_address INET DEFAULT NULL,
    check_hardware_id TEXT DEFAULT NULL,
    check_region TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    whitelist_found BOOLEAN := FALSE;
BEGIN
    SELECT EXISTS(
        SELECT 1 FROM blacklist_whitelist
        WHERE is_active = TRUE
          AND (expires_at IS NULL OR expires_at > NOW())
          AND (
              (check_ip_address IS NOT NULL AND ip_address = check_ip_address) OR
              (check_hardware_id IS NOT NULL AND hardware_id = check_hardware_id) OR
              (check_region IS NOT NULL AND region = check_region)
          )
    ) INTO whitelist_found;
    
    RETURN whitelist_found;
END;
$$ LANGUAGE plpgsql;

-- Function to update validation patterns
CREATE OR REPLACE FUNCTION update_validation_pattern(
    pattern_ip INET DEFAULT NULL,
    pattern_hwid TEXT DEFAULT NULL,
    pattern_region TEXT DEFAULT NULL,
    validation_success BOOLEAN DEFAULT TRUE,
    license_key_id UUID DEFAULT NULL,
    application_id UUID DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    pattern_key_val TEXT;
    pattern_type_val VARCHAR(50);
    window_duration INTERVAL := INTERVAL '1 hour';
    current_window_start TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Determine pattern key and type
    IF pattern_ip IS NOT NULL AND pattern_hwid IS NOT NULL THEN
        pattern_key_val := 'ip_hwid:' || pattern_ip::text || ':' || pattern_hwid;
        pattern_type_val := 'ip_hwid';
    ELSIF pattern_ip IS NOT NULL THEN
        pattern_key_val := 'ip:' || pattern_ip::text;
        pattern_type_val := 'ip';
    ELSIF pattern_hwid IS NOT NULL THEN
        pattern_key_val := 'hwid:' || pattern_hwid;
        pattern_type_val := 'hwid';
    ELSIF pattern_region IS NOT NULL THEN
        pattern_key_val := 'region:' || pattern_region;
        pattern_type_val := 'region';
    ELSE
        RETURN; -- No valid pattern
    END IF;
    
    -- Calculate current window start (hourly windows)
    current_window_start := date_trunc('hour', NOW());
    
    -- Insert or update pattern data
    INSERT INTO validation_patterns (
        pattern_key,
        pattern_type,
        ip_address,
        hardware_id,
        region,
        total_validations,
        failed_validations,
        unique_license_keys,
        unique_applications,
        last_validation_at,
        window_start,
        window_end
    ) VALUES (
        pattern_key_val,
        pattern_type_val,
        pattern_ip,
        pattern_hwid,
        pattern_region,
        1,
        CASE WHEN validation_success THEN 0 ELSE 1 END,
        CASE WHEN license_key_id IS NOT NULL THEN 1 ELSE 0 END,
        CASE WHEN application_id IS NOT NULL THEN 1 ELSE 0 END,
        NOW(),
        current_window_start,
        current_window_start + window_duration
    )
    ON CONFLICT (pattern_key, window_start)
    DO UPDATE SET
        total_validations = validation_patterns.total_validations + 1,
        failed_validations = validation_patterns.failed_validations + CASE WHEN validation_success THEN 0 ELSE 1 END,
        unique_license_keys = GREATEST(validation_patterns.unique_license_keys, CASE WHEN license_key_id IS NOT NULL THEN validation_patterns.unique_license_keys + 1 ELSE validation_patterns.unique_license_keys END),
        unique_applications = GREATEST(validation_patterns.unique_applications, CASE WHEN application_id IS NOT NULL THEN validation_patterns.unique_applications + 1 ELSE validation_patterns.unique_applications END),
        last_validation_at = NOW(),
        failure_rate = CASE 
            WHEN (validation_patterns.total_validations + 1) > 0 
            THEN ROUND(((validation_patterns.failed_validations + CASE WHEN validation_success THEN 0 ELSE 1 END)::decimal / (validation_patterns.total_validations + 1)::decimal) * 100, 2)
            ELSE 0 
        END,
        validation_frequency = ROUND((validation_patterns.total_validations + 1)::decimal / EXTRACT(EPOCH FROM (NOW() - validation_patterns.window_start)) * 60, 2),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to create automated rule
CREATE OR REPLACE FUNCTION create_auto_rule(
    rule_name VARCHAR(255),
    rule_type VARCHAR(50),
    target_type VARCHAR(50),
    trigger_conditions JSONB,
    rule_description TEXT DEFAULT NULL,
    action_type VARCHAR(50) DEFAULT 'blacklist',
    blacklist_duration_hours INTEGER DEFAULT NULL,
    blacklist_reason_template TEXT DEFAULT 'Automated: Rule triggered - {rule_name}',
    is_enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 100,
    cooldown_minutes INTEGER DEFAULT 60,
    max_triggers_per_hour INTEGER DEFAULT 10,
    require_admin_approval BOOLEAN DEFAULT FALSE,
    created_by_admin UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_rule_id UUID;
BEGIN
    INSERT INTO blacklist_auto_rules (
        rule_name,
        rule_description,
        rule_type,
        target_type,
        trigger_conditions,
        action_type,
        blacklist_duration_hours,
        blacklist_reason_template,
        is_enabled,
        priority,
        cooldown_minutes,
        max_triggers_per_hour,
        require_admin_approval,
        created_by_admin_id,
        last_modified_by_admin_id
    ) VALUES (
        rule_name,
        rule_description,
        rule_type,
        target_type,
        trigger_conditions,
        action_type,
        blacklist_duration_hours,
        blacklist_reason_template,
        is_enabled,
        priority,
        cooldown_minutes,
        max_triggers_per_hour,
        require_admin_approval,
        created_by_admin,
        created_by_admin
    )
    RETURNING id INTO new_rule_id;

    RETURN new_rule_id;
END;
$$ LANGUAGE plpgsql;

-- Function to check rules against pattern data
CREATE OR REPLACE FUNCTION check_auto_rules(
    check_ip INET DEFAULT NULL,
    check_hwid TEXT DEFAULT NULL,
    check_region TEXT DEFAULT NULL
)
RETURNS TABLE(
    rule_id UUID,
    rule_name VARCHAR(255),
    rule_type VARCHAR(50),
    target_type VARCHAR(50),
    action_type VARCHAR(50),
    should_trigger BOOLEAN,
    trigger_data JSONB
) AS $$
DECLARE
    rule_record RECORD;
    pattern_data RECORD;
    condition_met BOOLEAN;
    trigger_info JSONB;
BEGIN
    -- Check if target is whitelisted
    IF is_whitelisted(check_ip, check_hwid, check_region) THEN
        RETURN; -- Skip all rules for whitelisted targets
    END IF;

    -- Loop through enabled rules
    FOR rule_record IN
        SELECT * FROM blacklist_auto_rules
        WHERE is_enabled = TRUE
        ORDER BY priority ASC, created_at ASC
    LOOP
        condition_met := FALSE;
        trigger_info := '{}'::jsonb;

        -- Get relevant pattern data based on rule target type
        IF rule_record.target_type = 'ip' AND check_ip IS NOT NULL THEN
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'ip'
              AND ip_address = check_ip
              AND window_end > NOW()
            ORDER BY updated_at DESC
            LIMIT 1;
        ELSIF rule_record.target_type = 'hwid' AND check_hwid IS NOT NULL THEN
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'hwid'
              AND hardware_id = check_hwid
              AND window_end > NOW()
            ORDER BY updated_at DESC
            LIMIT 1;
        ELSIF rule_record.target_type = 'region' AND check_region IS NOT NULL THEN
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'region'
              AND region = check_region
              AND window_end > NOW()
            ORDER BY updated_at DESC
            LIMIT 1;
        END IF;

        -- Check rule conditions if pattern data exists
        IF FOUND THEN
            -- Check different rule types
            CASE rule_record.rule_type
                WHEN 'ip_failure_rate' THEN
                    IF (rule_record.trigger_conditions->>'failed_validations')::integer <= pattern_data.failed_validations
                       AND (rule_record.trigger_conditions->>'failure_rate')::decimal <= pattern_data.failure_rate THEN
                        condition_met := TRUE;
                        trigger_info := jsonb_build_object(
                            'failed_validations', pattern_data.failed_validations,
                            'failure_rate', pattern_data.failure_rate,
                            'total_validations', pattern_data.total_validations
                        );
                    END IF;

                WHEN 'hwid_license_hopping' THEN
                    IF (rule_record.trigger_conditions->>'unique_license_keys')::integer <= pattern_data.unique_license_keys THEN
                        condition_met := TRUE;
                        trigger_info := jsonb_build_object(
                            'unique_license_keys', pattern_data.unique_license_keys,
                            'total_validations', pattern_data.total_validations
                        );
                    END IF;

                WHEN 'burst_detection' THEN
                    IF (rule_record.trigger_conditions->>'validation_frequency')::decimal <= pattern_data.validation_frequency THEN
                        condition_met := TRUE;
                        trigger_info := jsonb_build_object(
                            'validation_frequency', pattern_data.validation_frequency,
                            'total_validations', pattern_data.total_validations
                        );
                    END IF;
            END CASE;
        END IF;

        -- Return rule if conditions are met
        RETURN QUERY SELECT
            rule_record.id,
            rule_record.rule_name,
            rule_record.rule_type,
            rule_record.target_type,
            rule_record.action_type,
            condition_met,
            trigger_info;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to execute automated rule
CREATE OR REPLACE FUNCTION execute_auto_rule(
    rule_id UUID,
    target_ip INET DEFAULT NULL,
    target_hwid TEXT DEFAULT NULL,
    target_region TEXT DEFAULT NULL,
    trigger_data JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
    rule_record RECORD;
    execution_id UUID;
    blacklist_entry_id UUID;
    blacklist_reason TEXT;
    expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get rule details
    SELECT * INTO rule_record FROM blacklist_auto_rules WHERE id = rule_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Rule not found: %', rule_id;
    END IF;

    -- Check cooldown period
    IF rule_record.last_triggered_at IS NOT NULL
       AND rule_record.last_triggered_at + INTERVAL '1 minute' * rule_record.cooldown_minutes > NOW() THEN
        RAISE EXCEPTION 'Rule is in cooldown period';
    END IF;

    -- Create execution log
    INSERT INTO blacklist_rule_executions (
        rule_id,
        trigger_data,
        matched_conditions,
        target_ip,
        target_hwid,
        target_region,
        action_taken,
        requires_approval
    ) VALUES (
        rule_id,
        trigger_data,
        rule_record.trigger_conditions,
        target_ip,
        target_hwid,
        target_region,
        rule_record.action_type,
        rule_record.require_admin_approval
    )
    RETURNING id INTO execution_id;

    -- Execute action if no approval required
    IF NOT rule_record.require_admin_approval AND rule_record.action_type = 'blacklist' THEN
        -- Prepare blacklist reason
        blacklist_reason := REPLACE(rule_record.blacklist_reason_template, '{rule_name}', rule_record.rule_name);

        -- Calculate expiration
        IF rule_record.blacklist_duration_hours IS NOT NULL THEN
            expires_at := NOW() + INTERVAL '1 hour' * rule_record.blacklist_duration_hours;
        END IF;

        -- Create blacklist entry
        SELECT add_blacklist_entry(
            target_ip,
            target_hwid,
            target_region,
            blacklist_reason,
            NULL, -- admin_id
            expires_at
        ) INTO blacklist_entry_id;

        -- Update execution log
        UPDATE blacklist_rule_executions
        SET
            blacklist_entry_id = blacklist_entry_id,
            execution_status = 'executed'
        WHERE id = execution_id;
    END IF;

    -- Update rule statistics
    UPDATE blacklist_auto_rules
    SET
        total_triggers = total_triggers + 1,
        last_triggered_at = NOW()
    WHERE id = rule_id;

    RETURN execution_id;
END;
$$ LANGUAGE plpgsql;
