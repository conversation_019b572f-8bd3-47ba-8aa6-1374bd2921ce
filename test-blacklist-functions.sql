-- Test script for blacklist functionality
-- Run this in your Supabase SQL editor to test the blacklist system

-- 1. Test table creation and constraints
SELECT 
    'Blacklist table structure' as test_name,
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'blacklist'
ORDER BY ordinal_position;

-- 2. Test adding blacklist entries
SELECT 'Testing add_blacklist_entry function' as test_name;

-- Test IP-only blacklist
SELECT 
    'IP-only blacklist test' as test_type,
    add_blacklist_entry(
        '*************'::inet,
        NULL,
        NULL,
        'Test IP blacklist',
        NULL,
        NULL
    ) as entry_id;

-- Test HWID-only blacklist
SELECT 
    'HWID-only blacklist test' as test_type,
    add_blacklist_entry(
        NULL,
        'TEST_HWID_123',
        NULL,
        'Test HWID blacklist',
        NULL,
        NULL
    ) as entry_id;

-- Test Region-only blacklist
SELECT 
    'Region-only blacklist test' as test_type,
    add_blacklist_entry(
        NULL,
        NULL,
        'CN',
        'Test region blacklist',
        NULL,
        NULL
    ) as entry_id;

-- Test combined IP+HWID blacklist
SELECT 
    'Combined IP+HWID blacklist test' as test_type,
    add_blacklist_entry(
        '********'::inet,
        'COMBINED_HWID_456',
        NULL,
        'Test combined blacklist',
        NULL,
        NOW() + INTERVAL '1 hour' -- Expires in 1 hour
    ) as entry_id;

-- 3. Test constraint validation (should fail)
DO $$
BEGIN
    BEGIN
        -- This should fail due to constraint
        PERFORM add_blacklist_entry(NULL, NULL, NULL, 'Should fail', NULL, NULL);
        RAISE NOTICE 'ERROR: Constraint test failed - should have thrown exception';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'SUCCESS: Constraint test passed - correctly rejected empty entry';
    END;
END $$;

-- 4. View created entries
SELECT 
    'Created blacklist entries' as test_name,
    id,
    ip_address,
    hardware_id,
    region,
    blacklist_reason,
    is_active,
    blocked_at,
    expires_at
FROM blacklist
ORDER BY created_at;

-- 5. Test is_blacklisted function
SELECT 'Testing is_blacklisted function' as test_name;

-- Test IP blacklist check
SELECT 
    'IP blacklist check' as test_type,
    *
FROM is_blacklisted('*************'::inet, NULL, NULL);

-- Test HWID blacklist check
SELECT 
    'HWID blacklist check' as test_type,
    *
FROM is_blacklisted(NULL, 'TEST_HWID_123', NULL);

-- Test Region blacklist check
SELECT 
    'Region blacklist check' as test_type,
    *
FROM is_blacklisted(NULL, NULL, 'CN');

-- Test combined check
SELECT 
    'Combined blacklist check' as test_type,
    *
FROM is_blacklisted('********'::inet, 'COMBINED_HWID_456', NULL);

-- Test non-blacklisted entries
SELECT 
    'Non-blacklisted check' as test_type,
    *
FROM is_blacklisted('*******'::inet, 'SAFE_HWID', 'US');

-- 6. Test lifting blacklist entries
SELECT 'Testing lift_blacklist_entry function' as test_name;

-- Get an entry ID to lift
DO $$
DECLARE
    test_entry_id UUID;
BEGIN
    SELECT id INTO test_entry_id FROM blacklist WHERE ip_address = '*************'::inet LIMIT 1;
    
    IF test_entry_id IS NOT NULL THEN
        PERFORM lift_blacklist_entry(test_entry_id, NULL, 'Test lift');
        RAISE NOTICE 'Lifted blacklist entry: %', test_entry_id;
    ELSE
        RAISE NOTICE 'No entry found to lift';
    END IF;
END $$;

-- 7. Verify lifted entry is no longer active
SELECT 
    'Lifted entry check' as test_type,
    *
FROM is_blacklisted('*************'::inet, NULL, NULL);

-- 8. View final state
SELECT 
    'Final blacklist state' as test_name,
    id,
    ip_address,
    hardware_id,
    region,
    blacklist_reason,
    is_active,
    lifted_at,
    lift_reason
FROM blacklist
ORDER BY created_at;

-- 9. Clean up test data (optional - uncomment to clean up)
-- DELETE FROM blacklist WHERE blacklist_reason LIKE 'Test%';

SELECT 'Blacklist function tests completed' as result;
