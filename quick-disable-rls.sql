-- Quick fix: Temporarily disable <PERSON><PERSON> to test if everything works
-- WARNING: This removes security temporarily - only for testing!

-- Disable RLS on applications table
ALTER TABLE applications DISABLE ROW LEVEL SECURITY;

-- Disable <PERSON><PERSON> on license_keys table  
ALTER TABLE license_keys DISABLE ROW LEVEL SECURITY;

-- Grant permissions
GRANT ALL ON applications TO anon, authenticated;
GRANT ALL ON license_keys TO anon, authenticated;

-- Test query
SELECT 'RLS disabled - testing should work now' as status;
