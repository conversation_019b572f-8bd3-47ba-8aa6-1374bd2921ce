-- =====================================================
-- Fix API Validation Usage Tracking
-- =====================================================
-- This script ensures the API validation endpoint properly tracks usage
-- Run this after the comprehensive usage count fix

-- Create enhanced validation function for API endpoint
CREATE OR REPLACE FUNCTION validate_license_key_api(
    p_app_id UUID,
    p_license_key TEXT,
    p_hardware_id TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL
)
RETURNS TABLE(
    valid BOOLEAN,
    message TEXT,
    key_data JSONB
) AS $$
DECLARE
    key_record RECORD;
    app_record RECORD;
    device_record RECORD;
    result_valid BOOLEAN := FALSE;
    result_message TEXT := 'Invalid license key';
    result_data JSONB := '{}'::jsonb;
BEGIN
    -- Get application record
    SELECT * INTO app_record 
    FROM applications 
    WHERE id = p_app_id AND is_active = TRUE;
    
    IF NOT FOUND THEN
        result_message := 'Application not found or inactive';
        RETURN QUERY SELECT result_valid, result_message, result_data;
        RETURN;
    END IF;
    
    -- Get license key record for this specific application
    SELECT * INTO key_record 
    FROM license_keys 
    WHERE license_key = p_license_key AND app_id = p_app_id;
    
    IF NOT FOUND THEN
        result_message := 'License key not found for this application';
        RETURN QUERY SELECT result_valid, result_message, result_data;
        RETURN;
    END IF;
    
    -- Check if key is revoked
    IF COALESCE(key_record.is_revoked, FALSE) = TRUE THEN
        result_message := CASE 
            WHEN key_record.revoked_reason IS NOT NULL AND key_record.revoked_reason != '' 
            THEN key_record.revoked_reason || ' Contact support at ' || COALESCE(app_record.discord_support, 'discord.gg/server') || ' if you believe this is an error.'
            ELSE 'License key has been revoked. Contact support at ' || COALESCE(app_record.discord_support, 'discord.gg/server') || ' if you believe this is an error.'
        END;
    -- Check if key is suspended (inactive)
    ELSIF COALESCE(key_record.is_active, TRUE) = FALSE THEN
        result_message := 'License key has been suspended. Contact support at ' || COALESCE(app_record.discord_support, 'discord.gg/server') || ' if you believe this is an error.';
    -- Check expiration
    ELSIF key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW() THEN
        result_message := 'License key has expired. Contact support at ' || COALESCE(app_record.discord_support, 'discord.gg/server') || ' if you believe this is an error.';
    -- Check hardware binding
    ELSIF p_hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NOT NULL 
          AND key_record.bound_hardware_id != p_hardware_id THEN
        -- Revoke the key for security
        UPDATE license_keys 
        SET is_revoked = TRUE, 
            revoked_at = NOW(),
            revoked_reason = 'Hardware ID mismatch detected'
        WHERE id = key_record.id;
        
        result_message := 'SECURITY VIOLATION: License key has been revoked due to hardware ID mismatch. This indicates potential key sharing or unauthorized usage. Contact support at ' || COALESCE(app_record.discord_support, 'discord.gg/server') || ' if you believe this is an error.';
    -- Check usage limits
    ELSIF key_record.max_uses IS NOT NULL AND COALESCE(key_record.current_uses, 0) >= key_record.max_uses THEN
        result_message := 'License key usage limit exceeded. Contact support at ' || COALESCE(app_record.discord_support, 'discord.gg/server') || ' if you believe this is an error.';
    ELSE
        -- Key is valid, proceed with validation
        result_valid := TRUE;
        result_message := 'License key validated successfully';
        
        -- Update license key usage statistics
        UPDATE license_keys
        SET current_uses = COALESCE(current_uses, 0) + 1,
            last_used_at = NOW(),
            bound_hardware_id = COALESCE(key_record.bound_hardware_id, p_hardware_id),
            bound_at = CASE 
                WHEN key_record.bound_hardware_id IS NULL AND p_hardware_id IS NOT NULL 
                THEN NOW() 
                ELSE key_record.bound_at 
            END,
            updated_at = NOW()
        WHERE id = key_record.id;
        
        -- Handle hardware device tracking
        IF p_hardware_id IS NOT NULL THEN
            -- Check if hardware device exists
            SELECT * INTO device_record
            FROM hardware_devices
            WHERE license_key_id = key_record.id AND hardware_id = p_hardware_id;
            
            IF NOT FOUND THEN
                -- Create new hardware device record
                INSERT INTO hardware_devices (
                    license_key_id,
                    hardware_id,
                    ip_address,
                    created_at,
                    updated_at
                ) VALUES (
                    key_record.id,
                    p_hardware_id,
                    p_ip_address,
                    NOW(),
                    NOW()
                );
            ELSE
                -- Update existing hardware device record
                UPDATE hardware_devices
                SET ip_address = COALESCE(p_ip_address, ip_address),
                    updated_at = NOW()
                WHERE id = device_record.id;
            END IF;
            
            -- Update or create user_license_keys record
            INSERT INTO user_license_keys (
                hardware_device_id,
                license_key_id,
                is_primary,
                total_uses,
                first_used_at,
                last_used_at,
                created_at,
                updated_at
            ) VALUES (
                COALESCE(device_record.id, (SELECT id FROM hardware_devices WHERE license_key_id = key_record.id AND hardware_id = p_hardware_id)),
                key_record.id,
                TRUE,
                COALESCE(key_record.current_uses, 0) + 1,
                NOW(),
                NOW(),
                NOW(),
                NOW()
            )
            ON CONFLICT (hardware_device_id, license_key_id)
            DO UPDATE SET
                total_uses = COALESCE(key_record.current_uses, 0) + 1,
                last_used_at = NOW(),
                updated_at = NOW();
        END IF;
        
        -- Build key data response
        result_data := jsonb_build_object(
            'tier', key_record.tier,
            'expires_at', key_record.expires_at,
            'current_uses', COALESCE(key_record.current_uses, 0) + 1,
            'max_uses', key_record.max_uses,
            'is_unlimited', (key_record.max_uses IS NULL),
            'is_expired', (key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW()),
            'is_usage_limit_reached', (key_record.max_uses IS NOT NULL AND COALESCE(key_record.current_uses, 0) + 1 >= key_record.max_uses),
            'bound_hardware_id', key_record.bound_hardware_id,
            'created_at', key_record.created_at
        );
    END IF;
    
    RETURN QUERY SELECT result_valid, result_message, result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_license_key_api(UUID, TEXT, TEXT, INET) TO anon, authenticated;

-- Create function to get accurate usage statistics for dashboard
CREATE OR REPLACE FUNCTION get_license_key_usage_stats(p_app_id UUID)
RETURNS TABLE(
    license_key_id UUID,
    license_key TEXT,
    current_uses INTEGER,
    max_uses INTEGER,
    last_used_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lk.id,
        lk.license_key,
        COALESCE(lk.current_uses, 0)::INTEGER,
        lk.max_uses,
        lk.last_used_at
    FROM license_keys lk
    WHERE lk.app_id = p_app_id
    ORDER BY lk.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_license_key_usage_stats(UUID) TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'API validation usage tracking fix completed!';
    RAISE NOTICE 'Created validate_license_key_api function for proper API usage tracking.';
    RAISE NOTICE 'Created get_license_key_usage_stats function for accurate dashboard display.';
END $$;
