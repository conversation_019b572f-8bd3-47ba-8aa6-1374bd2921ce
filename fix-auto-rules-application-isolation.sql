-- Fix Auto Rules Application Isolation
-- This migration adds the missing application_id column to blacklist_auto_rules table
-- to properly isolate auto rules by application (CRITICAL SECURITY FIX)

-- Step 1: Add the missing application_id column to blacklist_auto_rules table
ALTER TABLE blacklist_auto_rules 
ADD COLUMN IF NOT EXISTS application_id UUID;

-- Step 2: Add foreign key constraint to applications table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_auto_rules_application'
    ) THEN
        ALTER TABLE blacklist_auto_rules
        ADD CONSTRAINT fk_auto_rules_application
        FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Step 3: Create indexes for the new column
CREATE INDEX IF NOT EXISTS idx_auto_rules_application_id ON blacklist_auto_rules(application_id);
CREATE INDEX IF NOT EXISTS idx_auto_rules_app_enabled ON blacklist_auto_rules(application_id, is_enabled) WHERE is_enabled = TRUE;

-- Step 4: Update the create_auto_rule function to include application_id
CREATE OR REPLACE FUNCTION create_auto_rule(
    rule_name VARCHAR(255),
    rule_type VARCHAR(50),
    target_type VARCHAR(50),
    trigger_conditions JSONB,
    rule_description TEXT DEFAULT NULL,
    action_type VARCHAR(50) DEFAULT 'blacklist',
    blacklist_duration_hours INTEGER DEFAULT NULL,
    blacklist_reason_template TEXT DEFAULT 'Automated: Rule triggered - {rule_name}',
    is_enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 100,
    cooldown_minutes INTEGER DEFAULT 60,
    max_triggers_per_hour INTEGER DEFAULT 10,
    require_admin_approval BOOLEAN DEFAULT FALSE,
    created_by_admin UUID DEFAULT NULL,
    rule_application_id UUID DEFAULT NULL  -- NEW: Application ID parameter
)
RETURNS UUID AS $$
DECLARE
    new_rule_id UUID;
BEGIN
    INSERT INTO blacklist_auto_rules (
        rule_name,
        rule_description,
        rule_type,
        target_type,
        trigger_conditions,
        action_type,
        blacklist_duration_hours,
        blacklist_reason_template,
        is_enabled,
        priority,
        cooldown_minutes,
        max_triggers_per_hour,
        require_admin_approval,
        created_by_admin_id,
        last_modified_by_admin_id,
        application_id  -- NEW: Include application_id
    ) VALUES (
        rule_name,
        rule_description,
        rule_type,
        target_type,
        trigger_conditions,
        action_type,
        blacklist_duration_hours,
        blacklist_reason_template,
        is_enabled,
        priority,
        cooldown_minutes,
        max_triggers_per_hour,
        require_admin_approval,
        created_by_admin,
        created_by_admin,
        rule_application_id  -- NEW: Set application_id
    )
    RETURNING id INTO new_rule_id;

    RETURN new_rule_id;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Update the check_auto_rules function to filter by application_id
CREATE OR REPLACE FUNCTION check_auto_rules(
    check_ip INET DEFAULT NULL,
    check_hwid TEXT DEFAULT NULL,
    check_region TEXT DEFAULT NULL,
    check_application_id UUID DEFAULT NULL  -- NEW: Application ID parameter
)
RETURNS TABLE(
    rule_id UUID,
    rule_name VARCHAR(255),
    rule_type VARCHAR(50),
    target_type VARCHAR(50),
    action_type VARCHAR(50),
    should_trigger BOOLEAN,
    trigger_data JSONB
) AS $$
DECLARE
    rule_record RECORD;
    pattern_data RECORD;
    condition_met BOOLEAN;
    trigger_info JSONB;
BEGIN
    -- Check if target is whitelisted
    IF is_whitelisted(check_ip, check_hwid, check_region) THEN
        RETURN; -- Skip all rules for whitelisted targets
    END IF;

    -- Loop through enabled rules for this specific application
    FOR rule_record IN
        SELECT *
        FROM blacklist_auto_rules
        WHERE is_enabled = TRUE
          AND (application_id = check_application_id OR check_application_id IS NULL)  -- NEW: Filter by application
        ORDER BY priority ASC
    LOOP
        condition_met := FALSE;
        trigger_info := '{}'::jsonb;

        -- Check rule conditions based on rule type
        IF rule_record.rule_type = 'ip_failure_rate' AND rule_record.target_type = 'ip' AND check_ip IS NOT NULL THEN
            -- Get IP pattern data
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'ip'
              AND ip_address = check_ip
              AND (application_id = check_application_id OR check_application_id IS NULL)  -- NEW: Filter by application
            ORDER BY updated_at DESC
            LIMIT 1;

            IF pattern_data IS NOT NULL THEN
                -- Check failure rate conditions
                IF (rule_record.trigger_conditions->>'failed_validations')::integer <= pattern_data.failed_validations
                   AND (rule_record.trigger_conditions->>'failure_rate')::integer <= pattern_data.failure_rate THEN
                    condition_met := TRUE;
                    trigger_info := jsonb_build_object(
                        'failed_validations', pattern_data.failed_validations,
                        'failure_rate', pattern_data.failure_rate,
                        'total_validations', pattern_data.total_validations
                    );
                END IF;
            END IF;

        ELSIF rule_record.rule_type = 'hwid_license_hopping' AND rule_record.target_type = 'hwid' AND check_hwid IS NOT NULL THEN
            -- Get HWID pattern data
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'hwid'
              AND hardware_id = check_hwid
              AND (application_id = check_application_id OR check_application_id IS NULL)  -- NEW: Filter by application
            ORDER BY updated_at DESC
            LIMIT 1;

            IF pattern_data IS NOT NULL THEN
                -- Check license hopping conditions
                IF (rule_record.trigger_conditions->>'unique_license_keys')::integer <= pattern_data.unique_license_keys THEN
                    condition_met := TRUE;
                    trigger_info := jsonb_build_object(
                        'unique_license_keys', pattern_data.unique_license_keys,
                        'total_validations', pattern_data.total_validations
                    );
                END IF;
            END IF;

        ELSIF rule_record.rule_type = 'burst_detection' AND rule_record.target_type = 'ip' AND check_ip IS NOT NULL THEN
            -- Get IP pattern data for burst detection
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'ip'
              AND ip_address = check_ip
              AND (application_id = check_application_id OR check_application_id IS NULL)  -- NEW: Filter by application
            ORDER BY updated_at DESC
            LIMIT 1;

            IF pattern_data IS NOT NULL THEN
                -- Check burst detection conditions
                IF (rule_record.trigger_conditions->>'validation_frequency')::integer <= pattern_data.validation_frequency THEN
                    condition_met := TRUE;
                    trigger_info := jsonb_build_object(
                        'validation_frequency', pattern_data.validation_frequency,
                        'total_validations', pattern_data.total_validations
                    );
                END IF;
            END IF;
        END IF;

        -- Return rule result
        RETURN QUERY SELECT
            rule_record.id,
            rule_record.rule_name,
            rule_record.rule_type,
            rule_record.target_type,
            rule_record.action_type,
            condition_met,
            trigger_info;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Step 6: Add application_id to validation_patterns table as well
ALTER TABLE validation_patterns 
ADD COLUMN IF NOT EXISTS application_id UUID;

-- Add foreign key constraint for validation_patterns
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_validation_patterns_application'
    ) THEN
        ALTER TABLE validation_patterns
        ADD CONSTRAINT fk_validation_patterns_application
        FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE;
    END IF;
END $$;

-- Create indexes for validation_patterns
CREATE INDEX IF NOT EXISTS idx_validation_patterns_application_id ON validation_patterns(application_id);
CREATE INDEX IF NOT EXISTS idx_validation_patterns_app_type ON validation_patterns(application_id, pattern_type);

-- Step 7: Update the update_validation_pattern function to include application_id
CREATE OR REPLACE FUNCTION update_validation_pattern(
    pattern_ip INET DEFAULT NULL,
    pattern_hwid TEXT DEFAULT NULL,
    pattern_region TEXT DEFAULT NULL,
    pattern_license_key_id UUID DEFAULT NULL,
    pattern_application_id UUID DEFAULT NULL,  -- NEW: Application ID parameter
    validation_success BOOLEAN DEFAULT TRUE,
    validation_error TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    pattern_key TEXT;
    current_pattern RECORD;
    new_failed_count INTEGER;
    new_total_count INTEGER;
    new_failure_rate NUMERIC;
    new_frequency NUMERIC;
    time_window INTERVAL := '1 hour';
BEGIN
    -- Generate pattern key based on available identifiers
    pattern_key := COALESCE(
        pattern_ip::text,
        pattern_hwid,
        pattern_region,
        'unknown'
    );

    -- Update IP patterns
    IF pattern_ip IS NOT NULL THEN
        -- Get current pattern or create new one
        SELECT * INTO current_pattern
        FROM validation_patterns
        WHERE pattern_type = 'ip'
          AND ip_address = pattern_ip
          AND (validation_patterns.application_id = pattern_application_id OR (validation_patterns.application_id IS NULL AND pattern_application_id IS NULL))  -- NEW: Filter by application
        ORDER BY updated_at DESC
        LIMIT 1;

        -- Calculate new values
        new_total_count := COALESCE(current_pattern.total_validations, 0) + 1;
        new_failed_count := COALESCE(current_pattern.failed_validations, 0) + CASE WHEN validation_success THEN 0 ELSE 1 END;
        new_failure_rate := CASE WHEN new_total_count > 0 THEN (new_failed_count::NUMERIC / new_total_count::NUMERIC) * 100 ELSE 0 END;
        
        -- Calculate frequency (validations per hour)
        new_frequency := CASE 
            WHEN current_pattern.created_at IS NOT NULL THEN
                new_total_count::NUMERIC / GREATEST(EXTRACT(EPOCH FROM (NOW() - current_pattern.created_at)) / 3600, 0.01)
            ELSE new_total_count::NUMERIC
        END;

        -- Insert or update pattern
        INSERT INTO validation_patterns (
            pattern_key,
            pattern_type,
            ip_address,
            total_validations,
            failed_validations,
            failure_rate,
            validation_frequency,
            last_validation_success,
            last_validation_error,
            application_id,  -- NEW: Include application_id
            created_at,
            updated_at
        ) VALUES (
            pattern_key,
            'ip',
            pattern_ip,
            new_total_count,
            new_failed_count,
            new_failure_rate,
            new_frequency,
            validation_success,
            validation_error,
            pattern_application_id,  -- NEW: Set application_id
            COALESCE(current_pattern.created_at, NOW()),
            NOW()
        )
        ON CONFLICT (pattern_key, pattern_type)  -- Simplified conflict resolution for now
        DO UPDATE SET
            total_validations = new_total_count,
            failed_validations = new_failed_count,
            failure_rate = new_failure_rate,
            validation_frequency = new_frequency,
            last_validation_success = validation_success,
            last_validation_error = validation_error,
            updated_at = NOW();
    END IF;

    -- Similar updates for HWID patterns (abbreviated for space)
    IF pattern_hwid IS NOT NULL THEN
        -- Get current HWID pattern
        SELECT * INTO current_pattern
        FROM validation_patterns
        WHERE pattern_type = 'hwid'
          AND hardware_id = pattern_hwid
          AND (validation_patterns.application_id = pattern_application_id OR (validation_patterns.application_id IS NULL AND pattern_application_id IS NULL))
        ORDER BY updated_at DESC
        LIMIT 1;

        -- Update unique license keys count if we have a license key
        INSERT INTO validation_patterns (
            pattern_key,
            pattern_type,
            hardware_id,
            total_validations,
            failed_validations,
            unique_license_keys,
            last_validation_success,
            last_validation_error,
            application_id,
            created_at,
            updated_at
        ) VALUES (
            pattern_hwid,
            'hwid',
            pattern_hwid,
            COALESCE(current_pattern.total_validations, 0) + 1,
            COALESCE(current_pattern.failed_validations, 0) + CASE WHEN validation_success THEN 0 ELSE 1 END,
            COALESCE(current_pattern.unique_license_keys, 0) + CASE WHEN pattern_license_key_id IS NOT NULL THEN 1 ELSE 0 END,
            validation_success,
            validation_error,
            pattern_application_id,
            COALESCE(current_pattern.created_at, NOW()),
            NOW()
        )
        ON CONFLICT (pattern_key, pattern_type)  -- Simplified conflict resolution for now
        DO UPDATE SET
            total_validations = validation_patterns.total_validations + 1,
            failed_validations = validation_patterns.failed_validations + CASE WHEN validation_success THEN 0 ELSE 1 END,
            unique_license_keys = GREATEST(validation_patterns.unique_license_keys, COALESCE(current_pattern.unique_license_keys, 0) + CASE WHEN pattern_license_key_id IS NOT NULL THEN 1 ELSE 0 END),
            last_validation_success = validation_success,
            last_validation_error = validation_error,
            updated_at = NOW();
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Step 8: Grant permissions
GRANT EXECUTE ON FUNCTION create_auto_rule(VARCHAR, VARCHAR, VARCHAR, JSONB, TEXT, VARCHAR, INTEGER, TEXT, BOOLEAN, INTEGER, INTEGER, INTEGER, BOOLEAN, UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_auto_rules(INET, TEXT, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_validation_pattern(INET, TEXT, TEXT, UUID, UUID, BOOLEAN, TEXT) TO authenticated;

-- Step 9: Add comment for documentation
COMMENT ON COLUMN blacklist_auto_rules.application_id IS 'ID of the application this auto rule belongs to - CRITICAL for data isolation';
COMMENT ON COLUMN validation_patterns.application_id IS 'ID of the application this validation pattern belongs to - CRITICAL for data isolation';

-- Step 10: Clean up duplicate patterns and create unique constraints
DO $$
BEGIN
    -- Drop old constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'validation_patterns_pattern_key_pattern_type_key'
    ) THEN
        ALTER TABLE validation_patterns
        DROP CONSTRAINT validation_patterns_pattern_key_pattern_type_key;
    END IF;

    -- Clean up duplicate patterns by keeping only the most recent one for each pattern_key + pattern_type combination
    DELETE FROM validation_patterns
    WHERE id NOT IN (
        SELECT DISTINCT ON (pattern_key, pattern_type) id
        FROM validation_patterns
        ORDER BY pattern_key, pattern_type, updated_at DESC
    );

    -- Create a partial unique index instead of a constraint with COALESCE
    -- This handles the case where application_id might be NULL
    DROP INDEX IF EXISTS idx_validation_patterns_unique_per_app;
    CREATE UNIQUE INDEX idx_validation_patterns_unique_per_app
    ON validation_patterns (pattern_key, pattern_type, application_id)
    WHERE application_id IS NOT NULL;

    -- Create a separate unique index for NULL application_id cases (after cleanup)
    DROP INDEX IF EXISTS idx_validation_patterns_unique_null_app;
    CREATE UNIQUE INDEX idx_validation_patterns_unique_null_app
    ON validation_patterns (pattern_key, pattern_type)
    WHERE application_id IS NULL;

    RAISE NOTICE 'Cleaned up duplicate validation patterns and created unique indexes';
END $$;

-- Final step: Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Auto rules application isolation migration completed successfully!';
    RAISE NOTICE 'IMPORTANT: You must now update existing auto rules to assign them to specific applications.';
END $$;
