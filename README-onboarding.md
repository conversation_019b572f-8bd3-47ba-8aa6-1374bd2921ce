# Onboarding System Setup

This guide will help you set up the onboarding system for new users.

## 🚀 Quick Setup

### Step 1: Run Database Migration

1. Open your **Supabase Dashboard**
2. Go to **SQL Editor**
3. Copy and paste the contents of `add-onboarding-fields.sql`
4. Click **Run** to execute the migration

### Step 2: Verify Setup

The migration will:
- ✅ Add onboarding fields to `user_profiles` table
- ✅ Create username availability checking function
- ✅ Create onboarding completion function
- ✅ Set up proper RLS policies
- ✅ Create indexes for performance

## 📋 How It Works

### New User Flow:
1. **User registers** → Account created
2. **User logs in** → Redirected to dashboard
3. **Dashboard checks onboarding** → Shows onboarding modal if not completed
4. **User completes onboarding** → Must answer all questions:
   - **Age verification** (must be 16+)
   - **Username selection** (unique, 3-20 characters)
   - **Usage reason** (minimum 10 characters)
5. **Onboarding completed** → Access to dashboard granted

### Onboarding Requirements:
- ✅ **Age**: Must be 16 or older
- ✅ **Username**: 3-20 characters, alphanumeric + underscores only
- ✅ **Usage Reason**: At least 10 characters explaining their use case

### Database Fields Added:
```sql
-- New fields in user_profiles table
onboarding_completed BOOLEAN DEFAULT FALSE
age INTEGER
username VARCHAR(50) UNIQUE
usage_reason TEXT
onboarding_completed_at TIMESTAMP WITH TIME ZONE
```

## 🔒 Security Features

- **Age verification** prevents underage users
- **Username uniqueness** enforced at database level
- **RLS policies** ensure users can only access their own data
- **Input validation** on both frontend and backend
- **SQL injection protection** through parameterized queries

## 🎯 User Experience

- **Progressive disclosure** - one question at a time
- **Real-time validation** - username availability checking
- **Visual feedback** - progress bar and status indicators
- **Error handling** - clear error messages
- **Responsive design** - works on all devices

## 🛠️ API Endpoints

The system includes these API endpoints:

- `GET /api/onboarding` - Check onboarding status
- `POST /api/onboarding` - Complete onboarding
- `PUT /api/onboarding` - Check username availability

## 🚫 Access Control

Users **cannot access the dashboard** until they complete onboarding:
- Dashboard page checks onboarding status
- Shows onboarding modal if not completed
- Only grants access after all questions are answered
- Enforced on both frontend and backend

## 📊 Database Functions

### `is_username_available(username)`
Checks if a username is available (case-insensitive)

### `complete_onboarding(user_id, age, username, usage_reason)`
Completes the onboarding process with validation:
- Validates age requirement (16+)
- Checks username availability
- Stores onboarding data
- Marks onboarding as completed

## 🔍 Troubleshooting

### Common Issues:

1. **Migration fails**
   - Make sure you have admin access to Supabase
   - Check for existing conflicting columns
   - Run each section separately if needed

2. **Username check not working**
   - Verify the `is_username_available` function was created
   - Check RLS policies are properly set

3. **Onboarding modal not showing**
   - Check browser console for errors
   - Verify API endpoints are accessible
   - Ensure user is properly authenticated

### Testing the System:

1. Create a new account
2. Verify onboarding modal appears
3. Test age validation (try entering 15)
4. Test username availability
5. Complete onboarding and verify dashboard access

## 📝 Customization

You can customize the onboarding questions by:
1. Modifying the `OnboardingModal.tsx` component
2. Updating the database schema if needed
3. Adjusting validation rules in the API endpoints

The system is designed to be easily extensible for additional onboarding steps.
