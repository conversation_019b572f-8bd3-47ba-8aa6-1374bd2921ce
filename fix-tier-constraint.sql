-- =====================================================
-- FIX TIER CONSTRAINT TO ALLOW NULL TIERS
-- =====================================================
-- This migration removes the constraint that requires tiers to be NOT NULL
-- allowing applications to exist without any tier system

-- Remove the existing tier constraint that prevents NULL values
ALTER TABLE license_keys 
DROP CONSTRAINT IF EXISTS license_keys_tier_check;

-- Add a new constraint that allows NULL tiers
-- Only validates length if tier is provided (not NULL)
ALTER TABLE license_keys 
ADD CONSTRAINT license_keys_tier_check 
CHECK (tier IS NULL OR length(trim(tier)) > 0);

-- Update any existing 'basic' tiers to NULL if desired (optional)
-- Uncomment the line below if you want to remove all default 'basic' tiers
-- UPDATE license_keys SET tier = NULL WHERE tier = 'basic';

-- Verify the constraint works
-- This should succeed (NULL tier)
-- INSERT INTO license_keys (license_key, tier) VALUES ('TEST-NULL-TIER-0001', NULL);

-- This should fail (empty tier)
-- INSERT INTO license_keys (license_key, tier) VALUES ('TEST-EMPTY-TIER-0001', '');

-- Clean up test data (uncomment if you ran the test inserts above)
-- DELETE FROM license_keys WHERE license_key IN ('TEST-NULL-TIER-0001', 'TEST-EMPTY-TIER-0001');
