-- =====================================================
-- Comprehensive Fix for Usage Count Tracking Issues
-- =====================================================
-- This script fixes all usage count tracking issues in the KeyAuth system
-- Run this in your Supabase SQL Editor

-- Step 1: Ensure current_uses column exists and set default values
DO $$
BEGIN
    -- Add current_uses column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'license_keys' AND column_name = 'current_uses') THEN
        ALTER TABLE license_keys ADD COLUMN current_uses INTEGER DEFAULT 0;
        RAISE NOTICE 'Added current_uses column to license_keys table';
    ELSE
        RAISE NOTICE 'current_uses column already exists';
    END IF;

    -- Check if usage_count column exists and migrate data if needed
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'license_keys' AND column_name = 'usage_count') THEN
        -- Update current_uses with values from usage_count where current_uses is 0 or null
        UPDATE license_keys
        SET current_uses = COALESCE(usage_count, 0)
        WHERE COALESCE(current_uses, 0) = 0 AND COALESCE(usage_count, 0) > 0;

        RAISE NOTICE 'Migrated usage_count data to current_uses column';
    ELSE
        RAISE NOTICE 'usage_count column does not exist - no migration needed';
    END IF;

    -- Ensure all current_uses values are not null
    UPDATE license_keys
    SET current_uses = 0
    WHERE current_uses IS NULL;

    RAISE NOTICE 'Ensured all current_uses values are not null';
END $$;

-- Step 2: Create or replace the validate_license_key function with proper current_uses tracking
CREATE OR REPLACE FUNCTION validate_license_key(
    key_to_validate TEXT,
    hardware_id TEXT DEFAULT NULL
)
RETURNS TABLE(
    valid BOOLEAN,
    message TEXT,
    key_data JSONB
) AS $$
DECLARE
    key_record RECORD;
    result_valid BOOLEAN := FALSE;
    result_message TEXT := 'Invalid license key';
    result_data JSONB := '{}'::jsonb;
BEGIN
    -- Get license key record
    SELECT * INTO key_record 
    FROM license_keys 
    WHERE license_key = key_to_validate;
    
    IF NOT FOUND THEN
        result_message := 'License key not found';
    ELSE
        -- Check if key is revoked
        IF COALESCE(key_record.is_revoked, FALSE) = TRUE THEN
            result_message := CASE 
                WHEN key_record.revoked_reason IS NOT NULL AND key_record.revoked_reason != '' 
                THEN key_record.revoked_reason || ' Contact support at discord.gg/server if you believe this is an error.'
                ELSE 'License key has been revoked. Contact support at discord.gg/server if you believe this is an error.'
            END;
        -- Check if key is suspended (inactive)
        ELSIF COALESCE(key_record.is_active, TRUE) = FALSE THEN
            result_message := 'License key has been suspended. Contact support at discord.gg/server if you believe this is an error.';
        -- Check expiration
        ELSIF key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW() THEN
            result_message := 'License key has expired. Contact support at discord.gg/server if you believe this is an error.';
        -- Check hardware binding
        ELSIF hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NOT NULL 
              AND key_record.bound_hardware_id != hardware_id THEN
            -- Revoke the key for security
            UPDATE license_keys 
            SET is_revoked = TRUE, 
                revoked_at = NOW(),
                revoked_reason = 'Hardware ID mismatch detected'
            WHERE id = key_record.id;
            
            result_message := 'SECURITY VIOLATION: License key has been revoked due to hardware ID mismatch. This indicates potential key sharing or unauthorized usage. Contact support at discord.gg/server if you believe this is an error.';
        -- Check usage limits
        ELSIF key_record.max_uses IS NOT NULL AND COALESCE(key_record.current_uses, 0) >= key_record.max_uses THEN
            result_message := 'License key usage limit exceeded. Contact support at discord.gg/server if you believe this is an error.';
        ELSE
            -- Key is valid
            result_valid := TRUE;
            result_message := 'License key validated successfully';
            
            -- Update usage statistics (increment current_uses)
            UPDATE license_keys
            SET current_uses = COALESCE(current_uses, 0) + 1,
                last_used_at = NOW(),
                bound_hardware_id = COALESCE(key_record.bound_hardware_id, hardware_id),
                bound_at = CASE 
                    WHEN key_record.bound_hardware_id IS NULL AND hardware_id IS NOT NULL 
                    THEN NOW() 
                    ELSE key_record.bound_at 
                END,
                updated_at = NOW()
            WHERE id = key_record.id;
            
            -- Build key data response (with incremented current_uses)
            result_data := jsonb_build_object(
                'tier', key_record.tier,
                'expires_at', key_record.expires_at,
                'current_uses', COALESCE(key_record.current_uses, 0) + 1,
                'max_uses', key_record.max_uses,
                'is_unlimited', (key_record.max_uses IS NULL),
                'is_expired', (key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW()),
                'is_usage_limit_reached', (key_record.max_uses IS NOT NULL AND COALESCE(key_record.current_uses, 0) + 1 >= key_record.max_uses),
                'bound_hardware_id', key_record.bound_hardware_id,
                'created_at', key_record.created_at
            );
        END IF;
    END IF;
    
    RETURN QUERY SELECT result_valid, result_message, result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Update get_license_key_stats function to use current_uses
CREATE OR REPLACE FUNCTION get_license_key_stats()
RETURNS TABLE(
    total_keys INTEGER,
    active_keys INTEGER,
    expired_keys INTEGER,
    revoked_keys INTEGER,
    total_usage INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_active, TRUE) = TRUE AND COALESCE(is_revoked, FALSE) = FALSE)::INTEGER as active_keys,
        COUNT(*) FILTER (WHERE expires_at IS NOT NULL AND expires_at <= NOW())::INTEGER as expired_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_revoked, FALSE) = TRUE)::INTEGER as revoked_keys,
        COALESCE(SUM(current_uses), 0)::INTEGER as total_usage
    FROM license_keys;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Grant necessary permissions
GRANT EXECUTE ON FUNCTION validate_license_key(TEXT, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_license_key_stats() TO authenticated;

-- Step 5: Update any existing license keys that have null current_uses
UPDATE license_keys 
SET current_uses = 0 
WHERE current_uses IS NULL;

-- Step 6: Add index on current_uses for better performance
CREATE INDEX IF NOT EXISTS idx_license_keys_current_uses ON license_keys(current_uses);

-- Step 7: Verify the fix by showing some sample data
SELECT
    license_key,
    current_uses,
    max_uses,
    last_used_at,
    created_at
FROM license_keys
ORDER BY created_at DESC
LIMIT 5;

-- Step 8: Fix user license keys tracking to sync with license_keys.current_uses
-- Update user_license_keys.total_uses to match the actual usage from license_keys
UPDATE user_license_keys
SET total_uses = (
    SELECT COALESCE(lk.current_uses, 0)
    FROM license_keys lk
    WHERE lk.id = user_license_keys.license_key_id
)
WHERE EXISTS (
    SELECT 1 FROM license_keys lk
    WHERE lk.id = user_license_keys.license_key_id
);

-- Step 9: Create function to sync usage counts between tables
CREATE OR REPLACE FUNCTION sync_usage_counts()
RETURNS VOID AS $$
BEGIN
    -- Sync user_license_keys.total_uses with license_keys.current_uses
    UPDATE user_license_keys
    SET total_uses = (
        SELECT COALESCE(lk.current_uses, 0)
        FROM license_keys lk
        WHERE lk.id = user_license_keys.license_key_id
    )
    WHERE EXISTS (
        SELECT 1 FROM license_keys lk
        WHERE lk.id = user_license_keys.license_key_id
    );

    RAISE NOTICE 'Usage counts synchronized between license_keys and user_license_keys tables';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permission to sync function
GRANT EXECUTE ON FUNCTION sync_usage_counts() TO authenticated;

-- Step 10: Run the sync function
SELECT sync_usage_counts();

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Usage count tracking fix completed successfully!';
    RAISE NOTICE 'All license keys now use current_uses column for tracking usage.';
    RAISE NOTICE 'The validate_license_key function has been updated to properly increment current_uses.';
    RAISE NOTICE 'User license keys have been synchronized with license key usage counts.';
END $$;
