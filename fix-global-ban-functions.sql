-- Fix the global ban functions to resolve column ambiguity issues
-- Run this in Supabase SQL editor to fix the unban functionality

-- Fix the lift_global_ban function - column name conflict
CREATE OR REPLACE FUNCTION lift_global_ban(
    ban_ip_address INET,
    ban_hardware_id TEXT,
    lifted_by_admin UUID DEFAULT NULL,
    lift_reason TEXT DEFAULT 'Ban lifted by administrator'
)
RETURNS BOOLEAN AS $$
DECLARE
    ban_found BOOLEAN := FALSE;
    rows_affected INTEGER;
BEGIN
    -- Update the ban to inactive
    UPDATE global_bans
    SET
        is_active = FALSE,
        lifted_at = NOW(),
        lifted_by_admin_id = lifted_by_admin,
        lift_reason = lift_global_ban.lift_reason,  -- Explicitly qualify the parameter
        updated_at = NOW()
    WHERE ip_address = ban_ip_address
      AND hardware_id = ban_hardware_id
      AND is_active = TRUE;

    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    ban_found := rows_affected > 0;
    RETURN ban_found;
END;
$$ LANGUAGE plpgsql;

-- Fix the is_globally_banned function - column name conflict
CREATE OR REPLACE FUNCTION is_globally_banned(
    check_ip_address INET,
    check_hardware_id TEXT
)
RETURNS TABLE(
    banned BOOLEAN,
    ban_reason TEXT,
    banned_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    ban_record RECORD;
BEGIN
    -- Look for active global ban for this IP/HWID combination
    SELECT 
        gb.ban_reason,
        gb.banned_at,
        gb.expires_at
    INTO ban_record
    FROM global_bans gb
    WHERE gb.ip_address = check_ip_address
      AND gb.hardware_id = check_hardware_id
      AND gb.is_active = TRUE
      AND (gb.expires_at IS NULL OR gb.expires_at > NOW());
    
    IF FOUND THEN
        -- Return ban details
        RETURN QUERY SELECT 
            TRUE as banned,
            ban_record.ban_reason,
            ban_record.banned_at,
            ban_record.expires_at;
    ELSE
        -- Not banned
        RETURN QUERY SELECT 
            FALSE as banned,
            NULL::TEXT as ban_reason,
            NULL::TIMESTAMP WITH TIME ZONE as banned_at,
            NULL::TIMESTAMP WITH TIME ZONE as expires_at;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Test the fixed functions
DO $$
DECLARE
    test_ip INET := '*************'::inet;
    test_hwid TEXT := 'TEST_FIX_HWID';
    ban_id UUID;
    check_result RECORD;
    lift_result BOOLEAN;
BEGIN
    RAISE NOTICE '=== Testing Fixed Global Ban Functions ===';
    
    -- Clean up any existing test data first
    DELETE FROM global_bans WHERE hardware_id = test_hwid;
    
    -- Test 1: Create a global ban
    RAISE NOTICE 'Step 1: Creating test ban...';
    SELECT add_global_ban(
        test_ip,
        test_hwid,
        'Test ban for fixing functions',
        NULL
    ) INTO ban_id;
    
    RAISE NOTICE 'Created ban with ID: %', ban_id;
    
    -- Test 2: Check if the ban exists
    RAISE NOTICE 'Step 2: Checking if ban exists...';
    SELECT * INTO check_result FROM is_globally_banned(test_ip, test_hwid);
    RAISE NOTICE 'Ban check result - Banned: %, Reason: %', check_result.banned, check_result.ban_reason;
    
    -- Test 3: Lift the ban
    RAISE NOTICE 'Step 3: Lifting the ban...';
    SELECT lift_global_ban(
        test_ip,
        test_hwid,
        NULL,
        'Test ban lifted after fixing functions'
    ) INTO lift_result;
    
    RAISE NOTICE 'Lift ban result: %', lift_result;
    
    -- Test 4: Verify the ban was lifted
    RAISE NOTICE 'Step 4: Verifying ban was lifted...';
    SELECT * INTO check_result FROM is_globally_banned(test_ip, test_hwid);
    RAISE NOTICE 'Post-lift check - Banned: %, Reason: %', check_result.banned, check_result.ban_reason;
    
    -- Clean up
    DELETE FROM global_bans WHERE hardware_id = test_hwid;
    RAISE NOTICE 'Test completed and cleaned up successfully.';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: %', SQLERRM;
        -- Clean up on error
        DELETE FROM global_bans WHERE hardware_id = test_hwid;
END $$;
