-- Fix ON CONFLICT constraint issue in validation_patterns table
-- Add the missing unique constraint or simplify the function

-- Option 1: Add the missing unique constraint
-- First, clean up any duplicate entries
DELETE FROM validation_patterns 
WHERE id NOT IN (
    SELECT DISTINCT ON (pattern_key, pattern_type) id
    FROM validation_patterns
    ORDER BY pattern_key, pattern_type, updated_at DESC
);

-- Add the unique constraint
ALTER TABLE validation_patterns 
ADD CONSTRAINT validation_patterns_pattern_key_type_unique 
UNIQUE (pattern_key, pattern_type);

-- Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Added unique constraint for ON CONFLICT clause in validation_patterns';
END $$;
