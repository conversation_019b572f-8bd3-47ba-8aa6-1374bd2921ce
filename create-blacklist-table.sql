-- Create Blacklist Table for IP/HWID/Region Blocking
-- This table tracks blacklisted entries that prevent users from accessing the system
-- Unlike global_bans which requires both IP and HWID, blacklist entries can block by any single criteria

-- Create the blacklist table
CREATE TABLE IF NOT EXISTS blacklist (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Blacklist identifiers (at least one must be provided)
    ip_address INET,
    hardware_id TEXT,
    region TEXT, -- Country code or region identifier
    
    -- Blacklist metadata
    blacklist_reason TEXT NOT NULL DEFAULT 'Blocked by administrator',
    blocked_by_admin_id UUID, -- References the admin who created the blacklist entry
    blocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Optional expiration (NULL = permanent blacklist)
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Status tracking
    is_active BOOLEAN DEFAULT TRUE,
    lifted_at TIMESTAMP WITH TIME ZONE,
    lifted_by_admin_id UUID,
    lift_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure at least one identifier is provided
    CONSTRAINT blacklist_has_identifier CHECK (
        ip_address IS NOT NULL OR 
        hardware_id IS NOT NULL OR 
        region IS NOT NULL
    )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_blacklist_ip ON blacklist(ip_address) WHERE ip_address IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_blacklist_hwid ON blacklist(hardware_id) WHERE hardware_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_blacklist_region ON blacklist(region) WHERE region IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_blacklist_active ON blacklist(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_blacklist_expires ON blacklist(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_blacklist_created ON blacklist(created_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_blacklist_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_blacklist_updated_at
    BEFORE UPDATE ON blacklist
    FOR EACH ROW
    EXECUTE FUNCTION update_blacklist_updated_at();

-- Create function to check if IP/HWID/Region is blacklisted
CREATE OR REPLACE FUNCTION is_blacklisted(
    check_ip_address INET DEFAULT NULL,
    check_hardware_id TEXT DEFAULT NULL,
    check_region TEXT DEFAULT NULL
)
RETURNS TABLE(
    blacklisted BOOLEAN,
    blacklist_reason TEXT,
    blocked_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    blacklist_type TEXT
) AS $$
DECLARE
    blacklist_record RECORD;
BEGIN
    -- Look for active blacklist entries that match any of the provided criteria
    SELECT 
        bl.blacklist_reason,
        bl.blocked_at,
        bl.expires_at,
        CASE 
            WHEN bl.ip_address = check_ip_address THEN 'IP'
            WHEN bl.hardware_id = check_hardware_id THEN 'HWID'
            WHEN bl.region = check_region THEN 'REGION'
            ELSE 'UNKNOWN'
        END as match_type
    INTO blacklist_record
    FROM blacklist bl
    WHERE bl.is_active = TRUE
      AND (bl.expires_at IS NULL OR bl.expires_at > NOW())
      AND (
          (check_ip_address IS NOT NULL AND bl.ip_address = check_ip_address) OR
          (check_hardware_id IS NOT NULL AND bl.hardware_id = check_hardware_id) OR
          (check_region IS NOT NULL AND bl.region = check_region)
      )
    ORDER BY bl.created_at DESC
    LIMIT 1;
    
    IF FOUND THEN
        -- Return blacklist details
        RETURN QUERY SELECT 
            TRUE as blacklisted,
            blacklist_record.blacklist_reason,
            blacklist_record.blocked_at,
            blacklist_record.expires_at,
            blacklist_record.match_type;
    ELSE
        -- Not blacklisted
        RETURN QUERY SELECT 
            FALSE as blacklisted,
            NULL::TEXT as blacklist_reason,
            NULL::TIMESTAMP WITH TIME ZONE as blocked_at,
            NULL::TIMESTAMP WITH TIME ZONE as expires_at,
            NULL::TEXT as blacklist_type;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to add a blacklist entry
CREATE OR REPLACE FUNCTION add_blacklist_entry(
    blacklist_ip_address INET DEFAULT NULL,
    blacklist_hardware_id TEXT DEFAULT NULL,
    blacklist_region TEXT DEFAULT NULL,
    blacklist_reason TEXT DEFAULT 'Blocked by administrator',
    blocked_by_admin UUID DEFAULT NULL,
    blacklist_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_blacklist_id UUID;
BEGIN
    -- Validate that at least one identifier is provided
    IF blacklist_ip_address IS NULL AND blacklist_hardware_id IS NULL AND blacklist_region IS NULL THEN
        RAISE EXCEPTION 'At least one identifier (IP, HWID, or Region) must be provided';
    END IF;
    
    -- Insert the blacklist entry
    INSERT INTO blacklist (
        ip_address,
        hardware_id,
        region,
        blacklist_reason,
        blocked_by_admin_id,
        expires_at
    ) VALUES (
        blacklist_ip_address,
        blacklist_hardware_id,
        blacklist_region,
        blacklist_reason,
        blocked_by_admin,
        blacklist_expires_at
    )
    RETURNING id INTO new_blacklist_id;

    RETURN new_blacklist_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to lift/remove a blacklist entry
CREATE OR REPLACE FUNCTION lift_blacklist_entry(
    blacklist_entry_id UUID,
    lifted_by_admin UUID DEFAULT NULL,
    lift_reason TEXT DEFAULT 'Lifted by administrator'
)
RETURNS BOOLEAN AS $$
DECLARE
    entry_found BOOLEAN := FALSE;
    rows_affected INTEGER;
BEGIN
    -- Update the blacklist entry to inactive
    UPDATE blacklist
    SET
        is_active = FALSE,
        lifted_at = NOW(),
        lifted_by_admin_id = lifted_by_admin,
        lift_reason = lift_reason,
        updated_at = NOW()
    WHERE id = blacklist_entry_id
      AND is_active = TRUE;

    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    entry_found := rows_affected > 0;
    RETURN entry_found;
END;
$$ LANGUAGE plpgsql;

-- Verify the table was created successfully
SELECT
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'blacklist'
ORDER BY ordinal_position;
