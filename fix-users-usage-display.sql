-- =====================================================
-- Fix Users Section Usage Display
-- =====================================================
-- This script fixes the users section to show accurate usage statistics
-- Run this after the comprehensive usage count fix

-- Step 1: Update consolidated_users view to use license_keys.current_uses directly
DROP VIEW IF EXISTS consolidated_users;
CREATE VIEW consolidated_users AS
SELECT
    hd.id as hardware_device_id,
    hd.hardware_id,
    hd.ip_address,
    hd.is_banned,
    hd.created_at as first_used_at,
    hd.updated_at as last_used_at,

    -- Primary (most recent) license key info
    primary_lk.license_key as primary_license_key,
    primary_lk.tier as primary_tier,
    primary_ulk.last_used_at as primary_last_used_at,
    -- Use license_keys.current_uses instead of user_license_keys.total_uses for accuracy
    primary_lk.current_uses as primary_total_uses,

    -- Aggregate data
    COUNT(ulk.id) as total_license_keys_used,
    -- Sum current_uses from license_keys table for accurate totals
    SUM(COALESCE(lk_all.current_uses, 0)) as total_uses_across_all_keys,
    MAX(ulk.last_used_at) as most_recent_activity,

    -- App info
    primary_lk.app_id

FROM hardware_devices hd
LEFT JOIN user_license_keys primary_ulk ON hd.id = primary_ulk.hardware_device_id AND primary_ulk.is_primary = true
LEFT JOIN license_keys primary_lk ON primary_ulk.license_key_id = primary_lk.id
LEFT JOIN user_license_keys ulk ON hd.id = ulk.hardware_device_id
LEFT JOIN license_keys lk_all ON ulk.license_key_id = lk_all.id
GROUP BY
    hd.id, hd.hardware_id, hd.ip_address, hd.is_banned, hd.created_at, hd.updated_at,
    primary_lk.license_key, primary_lk.tier, primary_lk.app_id, primary_lk.current_uses,
    primary_ulk.last_used_at;

-- Step 2: Create function to sync user_license_keys with license_keys usage
CREATE OR REPLACE FUNCTION sync_user_license_usage()
RETURNS VOID AS $$
BEGIN
    -- Update user_license_keys.total_uses to match license_keys.current_uses
    UPDATE user_license_keys 
    SET total_uses = (
        SELECT COALESCE(lk.current_uses, 0)
        FROM license_keys lk 
        WHERE lk.id = user_license_keys.license_key_id
    ),
    updated_at = NOW()
    WHERE EXISTS (
        SELECT 1 FROM license_keys lk 
        WHERE lk.id = user_license_keys.license_key_id
    );
    
    RAISE NOTICE 'Synchronized user_license_keys.total_uses with license_keys.current_uses';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Create trigger to automatically sync usage when license_keys.current_uses changes
CREATE OR REPLACE FUNCTION sync_user_license_usage_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Update corresponding user_license_keys records when license key usage changes
    UPDATE user_license_keys 
    SET total_uses = NEW.current_uses,
        updated_at = NOW()
    WHERE license_key_id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS trigger_sync_user_license_usage ON license_keys;

-- Create trigger on license_keys table
CREATE TRIGGER trigger_sync_user_license_usage
    AFTER UPDATE OF current_uses ON license_keys
    FOR EACH ROW
    WHEN (OLD.current_uses IS DISTINCT FROM NEW.current_uses)
    EXECUTE FUNCTION sync_user_license_usage_trigger();

-- Step 4: Grant permissions
GRANT EXECUTE ON FUNCTION sync_user_license_usage() TO authenticated;
GRANT SELECT ON consolidated_users TO authenticated;

-- Step 5: Run initial sync
SELECT sync_user_license_usage();

-- Step 6: Create function to get accurate user usage statistics for API
CREATE OR REPLACE FUNCTION get_user_usage_stats(p_app_id UUID)
RETURNS TABLE(
    hardware_device_id UUID,
    hardware_id TEXT,
    ip_address INET,
    is_banned BOOLEAN,
    first_used_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    primary_license_key TEXT,
    primary_tier TEXT,
    primary_total_uses INTEGER,
    total_license_keys_used BIGINT,
    total_uses_across_all_keys BIGINT,
    most_recent_activity TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cu.hardware_device_id,
        cu.hardware_id,
        cu.ip_address,
        cu.is_banned,
        cu.first_used_at,
        cu.last_used_at,
        cu.primary_license_key,
        cu.primary_tier,
        COALESCE(cu.primary_total_uses, 0)::INTEGER,
        cu.total_license_keys_used,
        COALESCE(cu.total_uses_across_all_keys, 0)::BIGINT,
        cu.most_recent_activity
    FROM consolidated_users cu
    WHERE cu.app_id = p_app_id
    ORDER BY cu.most_recent_activity DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_user_usage_stats(UUID) TO authenticated;

-- Step 7: Verify the fix by showing sample data
SELECT 
    hardware_id,
    primary_license_key,
    primary_total_uses,
    total_uses_across_all_keys
FROM consolidated_users 
LIMIT 5;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Users section usage display fix completed!';
    RAISE NOTICE 'consolidated_users view now uses license_keys.current_uses for accurate usage display.';
    RAISE NOTICE 'Automatic synchronization trigger created to keep usage counts in sync.';
    RAISE NOTICE 'get_user_usage_stats function created for API usage.';
END $$;
