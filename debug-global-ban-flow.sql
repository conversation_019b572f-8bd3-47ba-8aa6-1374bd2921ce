-- Comprehensive debug script for Global Ban System
-- Run this step by step to identify where the issue is

-- STEP 1: Check if global_bans table exists and has correct structure
SELECT 
    '=== STEP 1: Table Structure ===' as step,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'global_bans'
ORDER BY ordinal_position;

-- STEP 2: Check if functions exist
SELECT 
    '=== STEP 2: Functions ===' as step,
    routine_name,
    routine_type
FROM information_schema.routines 
WHERE routine_name IN ('is_globally_banned', 'add_global_ban', 'lift_global_ban')
ORDER BY routine_name;

-- STEP 3: Check current hardware_devices data
SELECT 
    '=== STEP 3: Hardware Devices Sample ===' as step,
    id,
    hardware_id,
    ip_address,
    is_banned,
    created_at
FROM hardware_devices 
ORDER BY created_at DESC 
LIMIT 5;

-- STEP 4: Test creating a global ban with sample data
-- First, let's get a real hardware device from the system
DO $$
DECLARE
    test_ip INET;
    test_hwid TEXT;
    ban_id UUID;
    check_result RECORD;
BEGIN
    -- Get a real device from hardware_devices table
    SELECT ip_address, hardware_id INTO test_ip, test_hwid
    FROM hardware_devices 
    WHERE ip_address IS NOT NULL 
    AND hardware_id IS NOT NULL
    LIMIT 1;
    
    IF test_ip IS NOT NULL AND test_hwid IS NOT NULL THEN
        RAISE NOTICE '=== STEP 4: Testing with real device data ===';
        RAISE NOTICE 'Test IP: %, Test HWID: %', test_ip, test_hwid;
        
        -- Test 4a: Check if already banned
        SELECT * INTO check_result FROM is_globally_banned(test_ip, test_hwid);
        RAISE NOTICE 'Initial ban check - Banned: %', check_result.banned;
        
        -- Test 4b: Create a global ban
        SELECT add_global_ban(test_ip, test_hwid, 'Debug test ban', NULL) INTO ban_id;
        RAISE NOTICE 'Created ban with ID: %', ban_id;
        
        -- Test 4c: Check if ban is now active
        SELECT * INTO check_result FROM is_globally_banned(test_ip, test_hwid);
        RAISE NOTICE 'After ban creation - Banned: %, Reason: %', check_result.banned, check_result.ban_reason;
        
        -- Test 4d: Clean up - lift the ban
        PERFORM lift_global_ban(test_ip, test_hwid, NULL, 'Debug test cleanup');
        RAISE NOTICE 'Ban lifted for cleanup';
        
    ELSE
        RAISE NOTICE '=== STEP 4: No hardware devices found with IP/HWID data ===';
    END IF;
END $$;

-- STEP 5: Check if there are any existing global bans
SELECT 
    '=== STEP 5: Existing Global Bans ===' as step,
    COUNT(*) as total_bans,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_bans
FROM global_bans;

-- STEP 6: Show any existing global bans
SELECT 
    '=== STEP 6: Global Ban Records ===' as step,
    ip_address,
    hardware_id,
    ban_reason,
    is_active,
    banned_at,
    lifted_at
FROM global_bans 
ORDER BY banned_at DESC;

-- STEP 7: Test with specific IP/HWID values that might be in your system
-- Replace these with actual values from your test
SELECT 
    '=== STEP 7: Test Specific Values ===' as step,
    'Testing common IP addresses' as note;

-- Test common localhost/development IPs
SELECT 
    'localhost test' as test_type,
    *
FROM is_globally_banned('127.0.0.1'::inet, 'TEST_HWID');

SELECT 
    'local network test' as test_type,
    *
FROM is_globally_banned('*************'::inet, 'TEST_HWID');

-- STEP 8: Check if there are any validation logs that might show the actual IP/HWID being used
SELECT 
    '=== STEP 8: Recent Validation Logs ===' as step,
    ip_address,
    success,
    error_message,
    created_at
FROM license_validation_logs 
ORDER BY created_at DESC 
LIMIT 10;

-- STEP 9: Check for any hardware devices that might have been banned recently
SELECT 
    '=== STEP 9: Recently Banned Devices ===' as step,
    hd.hardware_id,
    hd.ip_address,
    hd.is_banned,
    hd.updated_at,
    lk.license_key
FROM hardware_devices hd
JOIN license_keys lk ON hd.license_key_id = lk.id
WHERE hd.is_banned = true
ORDER BY hd.updated_at DESC
LIMIT 5;

-- STEP 10: Final verification - create a test ban and immediately check it
DO $$
DECLARE
    test_result RECORD;
BEGIN
    RAISE NOTICE '=== STEP 10: Final Test ===';
    
    -- Create test ban
    PERFORM add_global_ban('********'::inet, 'FINAL_TEST_HWID', 'Final verification test', NULL);
    
    -- Check it immediately
    SELECT * INTO test_result FROM is_globally_banned('********'::inet, 'FINAL_TEST_HWID');
    
    RAISE NOTICE 'Final test result - Banned: %, Reason: %', test_result.banned, test_result.ban_reason;
    
    -- Clean up
    PERFORM lift_global_ban('********'::inet, 'FINAL_TEST_HWID', NULL, 'Final test cleanup');
    
    RAISE NOTICE 'Final test cleanup completed';
END $$;
