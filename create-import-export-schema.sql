-- Import/Export Schema for Blacklist System
-- This creates tables and functions to support bulk import/export operations

-- Create import jobs table to track bulk import operations
CREATE TABLE IF NOT EXISTS blacklist_import_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Job metadata
    filename TEXT NOT NULL,
    file_size INTEGER,
    total_rows INTEGER DEFAULT 0,
    processed_rows INTEGER DEFAULT 0,
    successful_rows INTEGER DEFAULT 0,
    failed_rows INTEGER DEFAULT 0,
    
    -- Job status
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Admin tracking
    created_by_admin_id UUID,
    
    -- Import options
    conflict_resolution VARCHAR(50) DEFAULT 'skip' CHECK (conflict_resolution IN ('skip', 'update', 'error')),
    validate_entries BOOLEAN DEFAULT TRUE,
    
    -- Results and errors
    error_message TEXT,
    import_summary JSONB,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create import errors table to track individual row failures
CREATE TABLE IF NOT EXISTS blacklist_import_errors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    import_job_id UUID NOT NULL REFERENCES blacklist_import_jobs(id) ON DELETE CASCADE,
    
    -- Error details
    row_number INTEGER NOT NULL,
    raw_data JSONB,
    error_message TEXT NOT NULL,
    error_type VARCHAR(50) NOT NULL, -- 'validation', 'constraint', 'duplicate', etc.
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_import_jobs_status ON blacklist_import_jobs(status);
CREATE INDEX IF NOT EXISTS idx_import_jobs_created_by ON blacklist_import_jobs(created_by_admin_id);
CREATE INDEX IF NOT EXISTS idx_import_errors_job_id ON blacklist_import_errors(import_job_id);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_import_job_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_import_job_updated_at
    BEFORE UPDATE ON blacklist_import_jobs
    FOR EACH ROW
    EXECUTE FUNCTION update_import_job_updated_at();

-- Function to create a new import job
CREATE OR REPLACE FUNCTION create_import_job(
    job_filename TEXT,
    job_file_size INTEGER DEFAULT NULL,
    job_total_rows INTEGER DEFAULT 0,
    created_by_admin UUID DEFAULT NULL,
    job_conflict_resolution VARCHAR(50) DEFAULT 'skip',
    job_validate_entries BOOLEAN DEFAULT TRUE
)
RETURNS UUID AS $$
DECLARE
    new_job_id UUID;
BEGIN
    INSERT INTO blacklist_import_jobs (
        filename,
        file_size,
        total_rows,
        created_by_admin_id,
        conflict_resolution,
        validate_entries
    ) VALUES (
        job_filename,
        job_file_size,
        job_total_rows,
        created_by_admin,
        job_conflict_resolution,
        job_validate_entries
    )
    RETURNING id INTO new_job_id;
    
    RETURN new_job_id;
END;
$$ LANGUAGE plpgsql;

-- Function to update import job progress
CREATE OR REPLACE FUNCTION update_import_job_progress(
    job_id UUID,
    new_status VARCHAR(50) DEFAULT NULL,
    new_processed_rows INTEGER DEFAULT NULL,
    new_successful_rows INTEGER DEFAULT NULL,
    new_failed_rows INTEGER DEFAULT NULL,
    new_error_message TEXT DEFAULT NULL,
    new_summary JSONB DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    job_found BOOLEAN := FALSE;
    rows_affected INTEGER;
BEGIN
    UPDATE blacklist_import_jobs
    SET
        status = COALESCE(new_status, status),
        processed_rows = COALESCE(new_processed_rows, processed_rows),
        successful_rows = COALESCE(new_successful_rows, successful_rows),
        failed_rows = COALESCE(new_failed_rows, failed_rows),
        error_message = COALESCE(new_error_message, error_message),
        import_summary = COALESCE(new_summary, import_summary),
        started_at = CASE WHEN new_status = 'processing' AND started_at IS NULL THEN NOW() ELSE started_at END,
        completed_at = CASE WHEN new_status IN ('completed', 'failed', 'cancelled') THEN NOW() ELSE completed_at END,
        updated_at = NOW()
    WHERE id = job_id;
    
    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    job_found := rows_affected > 0;
    RETURN job_found;
END;
$$ LANGUAGE plpgsql;

-- Function to add import error
CREATE OR REPLACE FUNCTION add_import_error(
    job_id UUID,
    error_row_number INTEGER,
    error_raw_data JSONB,
    error_message TEXT,
    error_type VARCHAR(50)
)
RETURNS UUID AS $$
DECLARE
    new_error_id UUID;
BEGIN
    INSERT INTO blacklist_import_errors (
        import_job_id,
        row_number,
        raw_data,
        error_message,
        error_type
    ) VALUES (
        job_id,
        error_row_number,
        error_raw_data,
        error_message,
        error_type
    )
    RETURNING id INTO new_error_id;
    
    RETURN new_error_id;
END;
$$ LANGUAGE plpgsql;

-- Function to bulk import blacklist entries
CREATE OR REPLACE FUNCTION bulk_import_blacklist_entries(
    job_id UUID,
    entries JSONB, -- Array of blacklist entry objects
    conflict_resolution VARCHAR(50) DEFAULT 'skip',
    validate_entries BOOLEAN DEFAULT TRUE
)
RETURNS JSONB AS $$
DECLARE
    entry JSONB;
    entry_index INTEGER := 0;
    successful_count INTEGER := 0;
    failed_count INTEGER := 0;
    skipped_count INTEGER := 0;
    new_entry_id UUID;
    existing_entry RECORD;
    result JSONB;
BEGIN
    -- Update job status to processing
    PERFORM update_import_job_progress(job_id, 'processing');
    
    -- Process each entry
    FOR entry IN SELECT * FROM jsonb_array_elements(entries)
    LOOP
        entry_index := entry_index + 1;
        
        BEGIN
            -- Validate entry has at least one identifier
            IF validate_entries AND 
               (entry->>'ip_address' IS NULL OR entry->>'ip_address' = '') AND
               (entry->>'hardware_id' IS NULL OR entry->>'hardware_id' = '') AND
               (entry->>'region' IS NULL OR entry->>'region' = '') THEN
                
                PERFORM add_import_error(
                    job_id,
                    entry_index,
                    entry,
                    'At least one identifier (IP, HWID, or Region) must be provided',
                    'validation'
                );
                failed_count := failed_count + 1;
                CONTINUE;
            END IF;
            
            -- Check for existing entries
            SELECT * INTO existing_entry
            FROM blacklist
            WHERE (ip_address = NULLIF(entry->>'ip_address', '')::inet OR ip_address IS NULL)
              AND (hardware_id = NULLIF(entry->>'hardware_id', '') OR hardware_id IS NULL)
              AND (region = NULLIF(entry->>'region', '') OR region IS NULL)
              AND is_active = TRUE
            LIMIT 1;
            
            IF FOUND THEN
                -- Handle conflict based on resolution strategy
                IF conflict_resolution = 'skip' THEN
                    skipped_count := skipped_count + 1;
                    CONTINUE;
                ELSIF conflict_resolution = 'error' THEN
                    PERFORM add_import_error(
                        job_id,
                        entry_index,
                        entry,
                        'Duplicate entry found',
                        'duplicate'
                    );
                    failed_count := failed_count + 1;
                    CONTINUE;
                ELSIF conflict_resolution = 'update' THEN
                    -- Update existing entry
                    UPDATE blacklist
                    SET
                        blacklist_reason = COALESCE(NULLIF(entry->>'blacklist_reason', ''), blacklist_reason),
                        expires_at = CASE 
                            WHEN entry->>'expires_at' IS NOT NULL AND entry->>'expires_at' != '' 
                            THEN (entry->>'expires_at')::timestamp with time zone 
                            ELSE expires_at 
                        END,
                        updated_at = NOW()
                    WHERE id = existing_entry.id;
                    successful_count := successful_count + 1;
                    CONTINUE;
                END IF;
            END IF;
            
            -- Create new entry
            SELECT add_blacklist_entry(
                NULLIF(entry->>'ip_address', '')::inet,
                NULLIF(entry->>'hardware_id', ''),
                NULLIF(entry->>'region', ''),
                COALESCE(NULLIF(entry->>'blacklist_reason', ''), 'Imported entry'),
                NULL, -- admin_id will be set from job
                CASE 
                    WHEN entry->>'expires_at' IS NOT NULL AND entry->>'expires_at' != '' 
                    THEN (entry->>'expires_at')::timestamp with time zone 
                    ELSE NULL 
                END
            ) INTO new_entry_id;
            
            successful_count := successful_count + 1;
            
        EXCEPTION
            WHEN OTHERS THEN
                PERFORM add_import_error(
                    job_id,
                    entry_index,
                    entry,
                    SQLERRM,
                    'exception'
                );
                failed_count := failed_count + 1;
        END;
    END LOOP;
    
    -- Update job with final results
    result := jsonb_build_object(
        'total_entries', entry_index,
        'successful', successful_count,
        'failed', failed_count,
        'skipped', skipped_count
    );
    
    PERFORM update_import_job_progress(
        job_id,
        'completed',
        entry_index,
        successful_count,
        failed_count,
        NULL,
        result
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to export blacklist entries with filters
CREATE OR REPLACE FUNCTION export_blacklist_entries(
    export_format VARCHAR(10) DEFAULT 'json', -- 'json' or 'csv'
    filter_active_only BOOLEAN DEFAULT TRUE,
    filter_ip_only BOOLEAN DEFAULT FALSE,
    filter_hwid_only BOOLEAN DEFAULT FALSE,
    filter_region_only BOOLEAN DEFAULT FALSE,
    filter_created_after TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    filter_created_before TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE(
    export_data JSONB
) AS $$
DECLARE
    query_text TEXT;
    result_data JSONB;
BEGIN
    -- Build dynamic query based on filters
    query_text := 'SELECT jsonb_agg(
        jsonb_build_object(
            ''id'', id,
            ''ip_address'', ip_address::text,
            ''hardware_id'', hardware_id,
            ''region'', region,
            ''blacklist_reason'', blacklist_reason,
            ''blocked_at'', blocked_at,
            ''expires_at'', expires_at,
            ''is_active'', is_active,
            ''lifted_at'', lifted_at,
            ''lift_reason'', lift_reason,
            ''created_at'', created_at,
            ''updated_at'', updated_at
        )
    ) FROM blacklist WHERE 1=1';

    -- Apply filters
    IF filter_active_only THEN
        query_text := query_text || ' AND is_active = TRUE';
    END IF;

    IF filter_ip_only THEN
        query_text := query_text || ' AND ip_address IS NOT NULL';
    END IF;

    IF filter_hwid_only THEN
        query_text := query_text || ' AND hardware_id IS NOT NULL';
    END IF;

    IF filter_region_only THEN
        query_text := query_text || ' AND region IS NOT NULL';
    END IF;

    IF filter_created_after IS NOT NULL THEN
        query_text := query_text || ' AND created_at >= ''' || filter_created_after || '''';
    END IF;

    IF filter_created_before IS NOT NULL THEN
        query_text := query_text || ' AND created_at <= ''' || filter_created_before || '''';
    END IF;

    query_text := query_text || ' ORDER BY created_at DESC';

    -- Execute query
    EXECUTE query_text INTO result_data;

    -- Return formatted data
    RETURN QUERY SELECT COALESCE(result_data, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql;

-- Function to get import job status
CREATE OR REPLACE FUNCTION get_import_job_status(job_id UUID)
RETURNS TABLE(
    id UUID,
    filename TEXT,
    status VARCHAR(50),
    total_rows INTEGER,
    processed_rows INTEGER,
    successful_rows INTEGER,
    failed_rows INTEGER,
    progress_percentage NUMERIC,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    import_summary JSONB,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        j.id,
        j.filename,
        j.status,
        j.total_rows,
        j.processed_rows,
        j.successful_rows,
        j.failed_rows,
        CASE
            WHEN j.total_rows > 0 THEN ROUND((j.processed_rows::numeric / j.total_rows::numeric) * 100, 2)
            ELSE 0
        END as progress_percentage,
        j.started_at,
        j.completed_at,
        j.error_message,
        j.import_summary,
        j.created_at
    FROM blacklist_import_jobs j
    WHERE j.id = job_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get import job errors
CREATE OR REPLACE FUNCTION get_import_job_errors(
    job_id UUID,
    limit_rows INTEGER DEFAULT 100,
    offset_rows INTEGER DEFAULT 0
)
RETURNS TABLE(
    row_number INTEGER,
    raw_data JSONB,
    error_message TEXT,
    error_type VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        e.row_number,
        e.raw_data,
        e.error_message,
        e.error_type,
        e.created_at
    FROM blacklist_import_errors e
    WHERE e.import_job_id = job_id
    ORDER BY e.row_number
    LIMIT limit_rows
    OFFSET offset_rows;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old import jobs
CREATE OR REPLACE FUNCTION cleanup_old_import_jobs(
    days_to_keep INTEGER DEFAULT 30
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM blacklist_import_jobs
    WHERE created_at < NOW() - INTERVAL '1 day' * days_to_keep
      AND status IN ('completed', 'failed', 'cancelled');

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
