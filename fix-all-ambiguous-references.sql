-- Fix all ambiguous column references in update_validation_pattern function
-- This creates a clean version that avoids all naming conflicts

-- First, add missing columns if they don't exist
ALTER TABLE validation_patterns 
ADD COLUMN IF NOT EXISTS last_validation_success BOOLEAN DEFAULT TRUE;

ALTER TABLE validation_patterns 
ADD COLUMN IF NOT EXISTS last_validation_error TEXT;

-- Drop the old function first (required when changing parameter names)
DROP FUNCTION IF EXISTS update_validation_pattern(INET, TEXT, TEXT, UUID, UUID, BOOLEAN, TEXT);

-- Create a completely rewritten function with clear parameter names
CREATE FUNCTION update_validation_pattern(
    p_ip INET DEFAULT NULL,
    p_hwid TEXT DEFAULT NULL,
    p_region TEXT DEFAULT NULL,
    p_license_key_id UUID DEFAULT NULL,
    p_application_id UUID DEFAULT NULL,
    p_success BOOLEAN DEFAULT TRUE,
    p_error TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    v_pattern_key TEXT;
    v_current_pattern RECORD;
    v_new_failed_count INTEGER;
    v_new_total_count INTEGER;
    v_new_failure_rate NUMERIC;
    v_new_frequency NUMERIC;
BEGIN
    -- Generate pattern key based on available identifiers
    v_pattern_key := COALESCE(
        p_ip::text,
        p_hwid,
        p_region,
        'unknown'
    );

    -- Update IP patterns
    IF p_ip IS NOT NULL THEN
        -- Get current pattern or create new one
        SELECT * INTO v_current_pattern
        FROM validation_patterns
        WHERE pattern_type = 'ip'
          AND ip_address = p_ip
          AND (application_id = p_application_id OR (application_id IS NULL AND p_application_id IS NULL))
        ORDER BY updated_at DESC
        LIMIT 1;

        -- Calculate new values
        v_new_total_count := COALESCE(v_current_pattern.total_validations, 0) + 1;
        v_new_failed_count := COALESCE(v_current_pattern.failed_validations, 0) + CASE WHEN p_success THEN 0 ELSE 1 END;
        v_new_failure_rate := CASE WHEN v_new_total_count > 0 THEN (v_new_failed_count::NUMERIC / v_new_total_count::NUMERIC) * 100 ELSE 0 END;
        
        -- Calculate frequency (validations per hour)
        v_new_frequency := CASE 
            WHEN v_current_pattern.created_at IS NOT NULL THEN
                v_new_total_count::NUMERIC / GREATEST(EXTRACT(EPOCH FROM (NOW() - v_current_pattern.created_at)) / 3600, 0.01)
            ELSE v_new_total_count::NUMERIC
        END;

        -- Insert or update pattern
        INSERT INTO validation_patterns (
            pattern_key,
            pattern_type,
            ip_address,
            total_validations,
            failed_validations,
            failure_rate,
            validation_frequency,
            last_validation_success,
            last_validation_error,
            application_id,
            created_at,
            updated_at
        ) VALUES (
            v_pattern_key,
            'ip',
            p_ip,
            v_new_total_count,
            v_new_failed_count,
            v_new_failure_rate,
            v_new_frequency,
            p_success,
            p_error,
            p_application_id,
            COALESCE(v_current_pattern.created_at, NOW()),
            NOW()
        )
        ON CONFLICT (pattern_key, pattern_type)
        DO UPDATE SET
            total_validations = v_new_total_count,
            failed_validations = v_new_failed_count,
            failure_rate = v_new_failure_rate,
            validation_frequency = v_new_frequency,
            last_validation_success = p_success,
            last_validation_error = p_error,
            application_id = p_application_id,
            updated_at = NOW();
    END IF;

    -- Update HWID patterns
    IF p_hwid IS NOT NULL THEN
        -- Get current HWID pattern
        SELECT * INTO v_current_pattern
        FROM validation_patterns
        WHERE pattern_type = 'hwid'
          AND hardware_id = p_hwid
          AND (application_id = p_application_id OR (application_id IS NULL AND p_application_id IS NULL))
        ORDER BY updated_at DESC
        LIMIT 1;

        -- Insert or update HWID pattern
        INSERT INTO validation_patterns (
            pattern_key,
            pattern_type,
            hardware_id,
            total_validations,
            failed_validations,
            unique_license_keys,
            last_validation_success,
            last_validation_error,
            application_id,
            created_at,
            updated_at
        ) VALUES (
            p_hwid,
            'hwid',
            p_hwid,
            COALESCE(v_current_pattern.total_validations, 0) + 1,
            COALESCE(v_current_pattern.failed_validations, 0) + CASE WHEN p_success THEN 0 ELSE 1 END,
            COALESCE(v_current_pattern.unique_license_keys, 0) + CASE WHEN p_license_key_id IS NOT NULL THEN 1 ELSE 0 END,
            p_success,
            p_error,
            p_application_id,
            COALESCE(v_current_pattern.created_at, NOW()),
            NOW()
        )
        ON CONFLICT (pattern_key, pattern_type)
        DO UPDATE SET
            total_validations = validation_patterns.total_validations + 1,
            failed_validations = validation_patterns.failed_validations + CASE WHEN p_success THEN 0 ELSE 1 END,
            unique_license_keys = GREATEST(validation_patterns.unique_license_keys, COALESCE(v_current_pattern.unique_license_keys, 0) + CASE WHEN p_license_key_id IS NOT NULL THEN 1 ELSE 0 END),
            last_validation_success = p_success,
            last_validation_error = p_error,
            application_id = p_application_id,
            updated_at = NOW();
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION update_validation_pattern(INET, TEXT, TEXT, UUID, UUID, BOOLEAN, TEXT) TO authenticated;

-- Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Fixed all ambiguous column references in update_validation_pattern function';
END $$;
