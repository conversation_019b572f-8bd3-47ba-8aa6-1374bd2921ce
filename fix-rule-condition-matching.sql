-- Fix rule condition matching in check_auto_rules function
-- This aligns the function logic with the actual rule conditions

CREATE OR REPLACE FUNCTION check_auto_rules(
    check_ip INET DEFAULT NULL,
    check_hwid TEXT DEFAULT NULL,
    check_region TEXT DEFAULT NULL,
    check_application_id UUID DEFAULT NULL
)
RETURNS TABLE(
    rule_id UUID,
    rule_name VARCHAR(255),
    rule_type VARCHAR(50),
    target_type VARCHAR(50),
    action_type VARCHAR(50),
    should_trigger BOOLEAN,
    trigger_data JSONB
) AS $$
DECLARE
    rule_record RECORD;
    pattern_data RECORD;
    condition_met BOOLEAN;
    trigger_info JSONB;
BEGIN
    -- Check if target is whitelisted
    IF is_whitelisted(check_ip, check_hwid, check_region) THEN
        RETURN; -- Skip all rules for whitelisted targets
    END IF;

    -- Loop through enabled rules for this specific application
    FOR rule_record IN
        SELECT *
        FROM blacklist_auto_rules
        WHERE is_enabled = TRUE
          AND (application_id = check_application_id OR check_application_id IS NULL)
        ORDER BY priority ASC
    LOOP
        condition_met := FALSE;
        trigger_info := '{}'::jsonb;

        -- Check rule conditions based on rule type
        IF rule_record.rule_type = 'ip_failure_rate' AND rule_record.target_type = 'ip' AND check_ip IS NOT NULL THEN
            -- Get IP pattern data
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'ip'
              AND ip_address = check_ip
              AND (application_id = check_application_id OR check_application_id IS NULL)
            ORDER BY updated_at DESC
            LIMIT 1;

            IF pattern_data IS NOT NULL THEN
                -- Check failure conditions - only check failed_validations (not failure_rate)
                IF (rule_record.trigger_conditions->>'failed_validations')::integer <= pattern_data.failed_validations THEN
                    condition_met := TRUE;
                    trigger_info := jsonb_build_object(
                        'failed_validations', pattern_data.failed_validations,
                        'failure_rate', pattern_data.failure_rate,
                        'total_validations', pattern_data.total_validations
                    );
                END IF;
            END IF;

        ELSIF rule_record.rule_type = 'hwid_license_hopping' AND rule_record.target_type = 'hwid' AND check_hwid IS NOT NULL THEN
            -- Get HWID pattern data
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'hwid'
              AND hardware_id = check_hwid
              AND (application_id = check_application_id OR check_application_id IS NULL)
            ORDER BY updated_at DESC
            LIMIT 1;

            IF pattern_data IS NOT NULL THEN
                -- Check license hopping conditions - use license_switches from rule conditions
                IF (rule_record.trigger_conditions->>'license_switches')::integer <= pattern_data.unique_license_keys THEN
                    condition_met := TRUE;
                    trigger_info := jsonb_build_object(
                        'unique_license_keys', pattern_data.unique_license_keys,
                        'total_validations', pattern_data.total_validations
                    );
                END IF;
            END IF;

        ELSIF rule_record.rule_type = 'burst_detection' AND rule_record.target_type = 'ip' AND check_ip IS NOT NULL THEN
            -- Get IP pattern data for burst detection
            SELECT * INTO pattern_data
            FROM validation_patterns
            WHERE pattern_type = 'ip'
              AND ip_address = check_ip
              AND (application_id = check_application_id OR check_application_id IS NULL)
            ORDER BY updated_at DESC
            LIMIT 1;

            IF pattern_data IS NOT NULL THEN
                -- Check burst detection conditions - use burst_threshold from rule conditions
                IF (rule_record.trigger_conditions->>'burst_threshold')::integer <= pattern_data.total_validations THEN
                    condition_met := TRUE;
                    trigger_info := jsonb_build_object(
                        'total_validations', pattern_data.total_validations,
                        'validation_frequency', pattern_data.validation_frequency
                    );
                END IF;
            END IF;
        END IF;

        -- Return rule result
        RETURN QUERY SELECT
            rule_record.id,
            rule_record.rule_name,
            rule_record.rule_type,
            rule_record.target_type,
            rule_record.action_type,
            condition_met,
            trigger_info;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION check_auto_rules(INET, TEXT, TEXT, UUID) TO authenticated;

-- Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Fixed rule condition matching in check_auto_rules function';
END $$;
