-- User Preferences Schema for KeyAuth System
-- This creates a table to store user preferences including blur settings
-- Run this in your Supabase SQL editor

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Blur preferences (default to false = blur disabled)
    blur_enabled BOOLEAN DEFAULT FALSE,
    blur_license_keys BOOLEAN DEFAULT FALSE,
    blur_users_section BOOLEAN DEFAULT FALSE,
    blur_credentials BOOLEAN DEFAULT FALSE,
    
    -- Future preferences can be added here
    theme VARCHAR(20) DEFAULT 'light' CHECK (theme IN ('light', 'dark', 'auto')),
    notifications_enabled BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one preference record per user
    UNIQUE(user_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- Enable Row Level Security
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only view their own preferences
CREATE POLICY "Users can view their own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own preferences
CREATE POLICY "Users can insert their own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own preferences
CREATE POLICY "Users can update their own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own preferences
CREATE POLICY "Users can delete their own preferences" ON user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON user_preferences TO authenticated;

-- Create function to get user preferences with defaults
CREATE OR REPLACE FUNCTION get_user_preferences(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    blur_enabled BOOLEAN,
    blur_license_keys BOOLEAN,
    blur_users_section BOOLEAN,
    blur_credentials BOOLEAN,
    theme VARCHAR(20),
    notifications_enabled BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Try to get existing preferences
    RETURN QUERY
    SELECT 
        up.id,
        up.user_id,
        up.blur_enabled,
        up.blur_license_keys,
        up.blur_users_section,
        up.theme,
        up.notifications_enabled,
        up.created_at,
        up.updated_at
    FROM user_preferences up
    WHERE up.user_id = user_uuid;

    -- If no preferences exist, return defaults
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT
            NULL::UUID as id,
            user_uuid as user_id,
            FALSE as blur_enabled,
            FALSE as blur_license_keys,
            FALSE as blur_users_section,
            FALSE as blur_credentials,
            'light'::VARCHAR(20) as theme,
            TRUE as notifications_enabled,
            NOW() as created_at,
            NOW() as updated_at;
    END IF;
END;
$$;

-- Create function to upsert user preferences
CREATE OR REPLACE FUNCTION upsert_user_preferences(
    user_uuid UUID,
    p_blur_enabled BOOLEAN DEFAULT NULL,
    p_blur_license_keys BOOLEAN DEFAULT NULL,
    p_blur_users_section BOOLEAN DEFAULT NULL,
    p_blur_credentials BOOLEAN DEFAULT NULL,
    p_theme VARCHAR(20) DEFAULT NULL,
    p_notifications_enabled BOOLEAN DEFAULT NULL
)
RETURNS user_preferences
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result user_preferences;
BEGIN
    -- Insert or update preferences
    INSERT INTO user_preferences (
        user_id,
        blur_enabled,
        blur_license_keys,
        blur_users_section,
        blur_credentials,
        theme,
        notifications_enabled,
        updated_at
    )
    VALUES (
        user_uuid,
        COALESCE(p_blur_enabled, FALSE),
        COALESCE(p_blur_license_keys, FALSE),
        COALESCE(p_blur_users_section, FALSE),
        COALESCE(p_blur_credentials, FALSE),
        COALESCE(p_theme, 'light'),
        COALESCE(p_notifications_enabled, TRUE),
        NOW()
    )
    ON CONFLICT (user_id) DO UPDATE SET
        blur_enabled = COALESCE(p_blur_enabled, user_preferences.blur_enabled),
        blur_license_keys = COALESCE(p_blur_license_keys, user_preferences.blur_license_keys),
        blur_users_section = COALESCE(p_blur_users_section, user_preferences.blur_users_section),
        blur_credentials = COALESCE(p_blur_credentials, user_preferences.blur_credentials),
        theme = COALESCE(p_theme, user_preferences.theme),
        notifications_enabled = COALESCE(p_notifications_enabled, user_preferences.notifications_enabled),
        updated_at = NOW()
    RETURNING * INTO result;
    
    RETURN result;
END;
$$;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_user_preferences(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION upsert_user_preferences(UUID, BOOLEAN, BOOLEAN, BOOLEAN, BOOLEAN, VARCHAR(20), BOOLEAN) TO authenticated;

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_preferences_updated_at();

-- Insert default preferences for existing users (optional)
-- Uncomment the following lines if you want to create default preferences for existing users
/*
INSERT INTO user_preferences (user_id, blur_enabled, blur_license_keys, blur_users_section)
SELECT 
    id,
    FALSE,
    FALSE,
    FALSE
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM user_preferences)
ON CONFLICT (user_id) DO NOTHING;
*/
