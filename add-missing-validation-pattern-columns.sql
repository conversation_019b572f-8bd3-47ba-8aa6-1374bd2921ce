-- Add missing columns to validation_patterns table
-- This fixes the "column does not exist" errors

-- Add missing columns to validation_patterns table
ALTER TABLE validation_patterns 
ADD COLUMN IF NOT EXISTS last_validation_success BOOLEAN DEFAULT TRUE;

ALTER TABLE validation_patterns 
ADD COLUMN IF NOT EXISTS last_validation_error TEXT;

-- Update the table comment
COMMENT ON COLUMN validation_patterns.last_validation_success IS 'Whether the last validation attempt was successful';
COMMENT ON COLUMN validation_patterns.last_validation_error IS 'Error message from the last validation attempt';

-- Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Added missing columns to validation_patterns table';
END $$;
