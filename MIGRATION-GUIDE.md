# KeyAuth Database Migration Guide

This guide will help you migrate your existing KeyAuth database to the new app-based credential system.

## 🚨 **IMPORTANT: Backup First!**

Before running any migration scripts, **create a full backup** of your database:

### Supabase Backup
1. Go to your Supabase dashboard
2. Navigate to Settings → Database
3. Click "Create backup" or use the CLI:
```bash
supabase db dump > keyauth_backup_$(date +%Y%m%d_%H%M%S).sql
```

### PostgreSQL Backup
```bash
pg_dump -h your-host -U your-user -d your-database > keyauth_backup_$(date +%Y%m%d_%H%M%S).sql
```

## 📋 **Migration Steps**

### Step 1: Review Current Database Structure

First, check your current database structure:

```sql
-- Check existing tables
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check license_keys structure
\d license_keys;

-- Count existing data
SELECT 
    (SELECT COUNT(*) FROM auth.users) as users,
    (SELECT COUNT(*) FROM license_keys) as license_keys;
```

### Step 2: Run the Migration Script

1. **Copy the migration script**: `migrate-to-app-based-system.sql`
2. **Open your database client** (Supabase SQL Editor, pgAdmin, etc.)
3. **Paste and execute** the entire migration script

The script will:
- ✅ Create new tables (`applications`, `license_validation_logs`, etc.)
- ✅ Add new columns to existing tables
- ✅ Create indexes for performance
- ✅ Set up Row Level Security (RLS)
- ✅ Create a default application for existing license keys
- ✅ Create helpful views for dashboard queries

### Step 3: Verify Migration Success

After running the migration, verify everything worked:

```sql
-- Check new tables were created
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('applications', 'license_validation_logs', 'hardware_devices', 'api_usage_stats');

-- Check your default application was created
SELECT app_id, app_secret, app_name, created_at FROM applications;

-- Verify license keys are linked to applications
SELECT 
    lk.license_key,
    a.app_name,
    a.app_id
FROM license_keys lk
JOIN applications a ON lk.app_id = a.id
LIMIT 5;

-- Check the dashboard view
SELECT * FROM app_dashboard_view;
```

### Step 4: Get Your App Credentials

Retrieve your new app credentials:

```sql
-- Get all applications and their credentials
SELECT 
    app_id,
    app_secret,
    app_name,
    platform,
    created_at,
    is_active
FROM applications
ORDER BY created_at;
```

**Save these credentials!** You'll need them for your .NET applications.

### Step 5: Update Your .NET Applications

Update your existing .NET applications to use the new credentials:

**Before:**
```csharp
var config = new KeyAuthConfig
{
    SupabaseUrl = "https://xyz.supabase.co",
    SupabaseKey = "eyJ0eXAiOiJKV1QiLCJhbGci...",
    // ... other settings
};
```

**After:**
```csharp
var config = new KeyAuthConfig
{
    AppId = "app_12345678",        // From applications table
    AppSecret = "sk_abcdef123456", // From applications table
    AppName = "My Desktop App",
    // ... other settings remain the same
};
```

### Step 6: Test License Validation

Test that license validation still works with your updated applications:

1. **Build your updated .NET application**
2. **Run a license validation test**
3. **Check the new validation logs**:

```sql
-- Check recent validation attempts
SELECT 
    lvl.created_at,
    lvl.validation_result,
    lvl.license_key,
    lvl.hardware_id,
    a.app_name
FROM license_validation_logs lvl
JOIN applications a ON lvl.app_id = a.id
ORDER BY lvl.created_at DESC
LIMIT 10;
```

## 🎯 **What Changed**

### New Database Structure

```
users (existing)
├── applications (new)
    ├── license_keys (enhanced)
    ├── license_validation_logs (new)
    ├── hardware_devices (new)
    └── api_usage_stats (new)
```

### Enhanced Features

- **App-based credentials**: Each app gets unique `app_id` and `app_secret`
- **Better security**: Hardware ID binding, device tracking
- **Analytics**: Detailed validation logs and usage statistics
- **Multi-app support**: Users can manage multiple applications
- **Audit trail**: Complete history of all validation attempts

### New Columns in `license_keys`

- `app_id` - Links to applications table
- `tier` - License tier (basic, premium, enterprise)
- `is_unlimited` - Whether license has unlimited usage
- `bound_hardware_id` - Hardware ID this license is bound to
- `bound_at` - When the license was bound to hardware
- `is_revoked` - Whether license has been revoked
- `revoked_at` - When license was revoked
- `revoked_reason` - Reason for revocation
- `notes` - Additional notes about the license

## 🔧 **Creating Additional Applications**

After migration, you can create additional applications:

```sql
-- Create a new application
INSERT INTO applications (
    user_id,
    app_id,
    app_secret,
    app_name,
    app_description,
    platform,
    discord_support
) VALUES (
    auth.uid(), -- Your user ID
    generate_app_id(),
    generate_app_secret(),
    'My New Desktop App',
    'Description of my new application',
    'desktop',
    'discord.gg/server'
);
```

## 🚨 **Troubleshooting**

### Migration Failed?

If the migration fails:

1. **Check the error message** in your database client
2. **Restore from backup** if needed
3. **Fix the issue** and try again
4. **Contact support** if you need help

### License Validation Not Working?

1. **Check app credentials** are correct in your .NET app
2. **Verify license keys** are linked to applications:
   ```sql
   SELECT COUNT(*) FROM license_keys WHERE app_id IS NULL;
   ```
3. **Check validation logs** for error details
4. **Test connection** to the API endpoints

### Need to Rollback?

If you need to revert the changes:

1. **Run the rollback script**: `rollback-app-based-system.sql`
2. **Restore from backup** if needed
3. **Update your .NET apps** back to the old configuration

## 📞 **Support**

If you encounter any issues during migration:

1. **Check the validation logs** for detailed error messages
2. **Review this guide** for common solutions
3. **Join our Discord** for community support
4. **Contact support** with your specific error details

## ✅ **Migration Checklist**

- [ ] Database backup created
- [ ] Migration script executed successfully
- [ ] New tables and columns verified
- [ ] App credentials retrieved and saved
- [ ] .NET applications updated with new credentials
- [ ] License validation tested and working
- [ ] Validation logs showing successful attempts
- [ ] Dashboard views working (if implemented)

Once all items are checked, your migration to the app-based system is complete! 🎉
