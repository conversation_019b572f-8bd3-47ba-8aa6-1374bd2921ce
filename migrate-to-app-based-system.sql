-- =====================================================
-- KeyAuth Database Migration: App-Based System
-- =====================================================
-- This script migrates your existing KeyAuth database to the new app-based credential system
-- Run this script in your Supabase SQL editor or PostgreSQL client

-- =====================================================
-- STEP 1: CREATE NEW TABLES
-- =====================================================

-- Applications table (new)
CREATE TABLE IF NOT EXISTS applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    app_id VARCHAR(50) UNIQUE NOT NULL, -- Public identifier (app_12345678)
    app_secret VARCHAR(100) UNIQUE NOT NULL, -- Secret key (sk_abcdef123456)
    app_name VARCHAR(255) NOT NULL,
    app_description TEXT,
    platform VARCHAR(50) DEFAULT 'desktop', -- desktop, web, mobile, console
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- App settings
    hwid_method VARCHAR(50) DEFAULT 'comprehensive', -- comprehensive, simple, mac_address
    cache_duration_seconds INTEGER DEFAULT 3,
    discord_support VARCHAR(255),
    
    -- Usage statistics
    total_validations INTEGER DEFAULT 0,
    successful_validations INTEGER DEFAULT 0,
    failed_validations INTEGER DEFAULT 0,
    
    -- Security settings
    max_devices_per_key INTEGER DEFAULT 1,
    allow_vm_usage BOOLEAN DEFAULT FALSE,
    require_hwid_binding BOOLEAN DEFAULT TRUE
);

-- Update existing license_keys table to reference applications
ALTER TABLE license_keys 
ADD COLUMN IF NOT EXISTS app_id UUID REFERENCES applications(id) ON DELETE CASCADE;

-- License validation logs table (enhanced)
CREATE TABLE IF NOT EXISTS license_validation_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    app_id UUID REFERENCES applications(id) ON DELETE CASCADE,
    license_key_id UUID REFERENCES license_keys(id) ON DELETE SET NULL,
    license_key VARCHAR(255) NOT NULL, -- Store key even if record is deleted
    hardware_id VARCHAR(255) NOT NULL,
    validation_result VARCHAR(50) NOT NULL, -- success, invalid_key, expired, revoked, hwid_mismatch
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Additional context
    error_message TEXT,
    security_flags JSONB, -- Store security-related flags
    response_time_ms INTEGER
);

-- Hardware devices table (for tracking bound devices)
CREATE TABLE IF NOT EXISTS hardware_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    license_key_id UUID NOT NULL REFERENCES license_keys(id) ON DELETE CASCADE,
    hardware_id VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Device information
    os_info TEXT,
    cpu_info TEXT,
    mac_address VARCHAR(17),
    system_uuid VARCHAR(255),
    
    -- Security tracking
    validation_count INTEGER DEFAULT 0,
    suspicious_activity_count INTEGER DEFAULT 0,
    is_flagged BOOLEAN DEFAULT FALSE,
    
    UNIQUE(license_key_id, hardware_id)
);

-- API usage statistics table
CREATE TABLE IF NOT EXISTS api_usage_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    app_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    endpoint VARCHAR(100) NOT NULL,
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    avg_response_time_ms DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(app_id, date, endpoint)
);

-- =====================================================
-- STEP 2: ADD MISSING COLUMNS TO EXISTING TABLES
-- =====================================================

-- Enhance license_keys table
ALTER TABLE license_keys
ADD COLUMN IF NOT EXISTS tier VARCHAR(50) DEFAULT 'basic',
ADD COLUMN IF NOT EXISTS is_unlimited BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS bound_hardware_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS bound_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS created_by_user_id UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Update license_keys to have proper status tracking
ALTER TABLE license_keys
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS is_revoked BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS revoked_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS revoked_reason TEXT;

-- Rename usage_count to current_uses for clarity
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'license_keys' AND column_name = 'usage_count') THEN
        ALTER TABLE license_keys RENAME COLUMN usage_count TO current_uses;
    END IF;
END $$;

-- Add current_uses column if it doesn't exist
ALTER TABLE license_keys 
ADD COLUMN IF NOT EXISTS current_uses INTEGER DEFAULT 0;

-- =====================================================
-- STEP 3: CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to generate app_id
CREATE OR REPLACE FUNCTION generate_app_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'app_' || substr(md5(random()::text || clock_timestamp()::text), 1, 8);
END;
$$ LANGUAGE plpgsql;

-- Function to generate app_secret
CREATE OR REPLACE FUNCTION generate_app_secret()
RETURNS TEXT AS $$
BEGIN
    RETURN 'sk_' || substr(md5(random()::text || clock_timestamp()::text), 1, 32);
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- STEP 4: CREATE TRIGGERS
-- =====================================================

-- Triggers for updated_at
DROP TRIGGER IF EXISTS update_applications_updated_at ON applications;
CREATE TRIGGER update_applications_updated_at 
    BEFORE UPDATE ON applications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_license_keys_updated_at ON license_keys;
CREATE TRIGGER update_license_keys_updated_at 
    BEFORE UPDATE ON license_keys 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STEP 5: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Applications indexes
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications(user_id);
CREATE INDEX IF NOT EXISTS idx_applications_app_id ON applications(app_id);
CREATE INDEX IF NOT EXISTS idx_applications_app_secret ON applications(app_secret);
CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications(created_at);
CREATE INDEX IF NOT EXISTS idx_applications_is_active ON applications(is_active);

-- Enhanced license keys indexes
CREATE INDEX IF NOT EXISTS idx_license_keys_app_id ON license_keys(app_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_bound_hardware_id ON license_keys(bound_hardware_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_tier ON license_keys(tier);
CREATE INDEX IF NOT EXISTS idx_license_keys_is_revoked ON license_keys(is_revoked);
-- Only create this index if is_active column exists (it will after our migration)
CREATE INDEX IF NOT EXISTS idx_license_keys_status ON license_keys(is_active, is_revoked);

-- Validation logs indexes
CREATE INDEX IF NOT EXISTS idx_validation_logs_app_id ON license_validation_logs(app_id);
CREATE INDEX IF NOT EXISTS idx_validation_logs_license_key_id ON license_validation_logs(license_key_id);
CREATE INDEX IF NOT EXISTS idx_validation_logs_created_at ON license_validation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_validation_logs_hardware_id ON license_validation_logs(hardware_id);
CREATE INDEX IF NOT EXISTS idx_validation_logs_validation_result ON license_validation_logs(validation_result);
CREATE INDEX IF NOT EXISTS idx_validation_logs_license_key ON license_validation_logs(license_key);

-- Hardware devices indexes
CREATE INDEX IF NOT EXISTS idx_hardware_devices_license_key_id ON hardware_devices(license_key_id);
CREATE INDEX IF NOT EXISTS idx_hardware_devices_hardware_id ON hardware_devices(hardware_id);
CREATE INDEX IF NOT EXISTS idx_hardware_devices_last_seen ON hardware_devices(last_seen);
CREATE INDEX IF NOT EXISTS idx_hardware_devices_is_active ON hardware_devices(is_active);

-- API usage stats indexes
CREATE INDEX IF NOT EXISTS idx_api_usage_stats_app_id_date ON api_usage_stats(app_id, date);
CREATE INDEX IF NOT EXISTS idx_api_usage_stats_endpoint ON api_usage_stats(endpoint);

-- =====================================================
-- STEP 6: CREATE DEFAULT APPLICATION FOR EXISTING DATA
-- =====================================================

-- Create a default application for existing license keys
DO $$
DECLARE
    default_app_id UUID;
    admin_user_id UUID;
BEGIN
    -- Get the first admin user or create a system user
    SELECT id INTO admin_user_id FROM auth.users LIMIT 1;
    
    IF admin_user_id IS NULL THEN
        -- If no users exist, we'll need to handle this manually
        RAISE NOTICE 'No users found. You will need to create an application manually after creating your first user.';
    ELSE
        -- Create default application for existing license keys
        INSERT INTO applications (
            user_id,
            app_id,
            app_secret,
            app_name,
            app_description,
            platform,
            discord_support,
            total_validations,
            successful_validations,
            failed_validations
        ) VALUES (
            admin_user_id,
            generate_app_id(),
            generate_app_secret(),
            'Legacy Application',
            'Default application for existing license keys',
            'desktop',
            'discord.gg/server',
            0,
            0,
            0
        ) RETURNING id INTO default_app_id;
        
        -- Link existing license keys to the default application
        UPDATE license_keys 
        SET app_id = default_app_id 
        WHERE app_id IS NULL;
        
        RAISE NOTICE 'Created default application with ID: %', default_app_id;
        RAISE NOTICE 'All existing license keys have been linked to the default application.';
    END IF;
END $$;

-- =====================================================
-- STEP 7: CREATE USEFUL VIEWS
-- =====================================================

-- Application dashboard view
CREATE OR REPLACE VIEW app_dashboard_view AS
SELECT 
    a.id,
    a.app_id,
    a.app_name,
    a.platform,
    a.created_at,
    a.last_used,
    a.is_active,
    u.email as owner_email,
    COUNT(lk.id) as total_license_keys,
    COUNT(CASE WHEN COALESCE(lk.is_active, TRUE) = TRUE AND COALESCE(lk.is_revoked, FALSE) = FALSE THEN 1 END) as active_license_keys,
    a.total_validations,
    a.successful_validations,
    a.failed_validations,
    CASE 
        WHEN a.total_validations > 0 THEN 
            ROUND((a.successful_validations::DECIMAL / a.total_validations) * 100, 2)
        ELSE 0 
    END as success_rate
FROM applications a
JOIN auth.users u ON a.user_id = u.id
LEFT JOIN license_keys lk ON a.id = lk.app_id
GROUP BY a.id, u.email;

-- License key management view
CREATE OR REPLACE VIEW license_key_management_view AS
SELECT 
    lk.id,
    lk.license_key,
    lk.tier,
    lk.created_at,
    lk.expires_at,
    lk.max_uses,
    lk.current_uses,
    lk.is_unlimited,
    COALESCE(lk.is_active, TRUE) as is_active,
    COALESCE(lk.is_revoked, FALSE) as is_revoked,
    lk.bound_hardware_id,
    lk.bound_at,
    a.app_name,
    a.app_id,
    u.email as app_owner,
    CASE 
        WHEN lk.expires_at IS NOT NULL AND lk.expires_at <= NOW() THEN TRUE
        ELSE FALSE
    END as is_expired,
    CASE 
        WHEN lk.max_uses IS NOT NULL AND lk.current_uses >= lk.max_uses THEN TRUE
        ELSE FALSE
    END as is_usage_limit_reached
FROM license_keys lk
JOIN applications a ON lk.app_id = a.id
JOIN auth.users u ON a.user_id = u.id;

-- =====================================================
-- STEP 8: SET UP ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE license_validation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE hardware_devices ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage_stats ENABLE ROW LEVEL SECURITY;

-- Applications policies
CREATE POLICY "Users can view their own applications" ON applications
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own applications" ON applications
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own applications" ON applications
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own applications" ON applications
    FOR DELETE USING (auth.uid() = user_id);

-- License keys policies (updated)
DROP POLICY IF EXISTS "Users can view their own license keys" ON license_keys;
CREATE POLICY "Users can view their own license keys" ON license_keys
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM applications a 
            WHERE a.id = license_keys.app_id 
            AND a.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Users can insert license keys for their apps" ON license_keys;
CREATE POLICY "Users can insert license keys for their apps" ON license_keys
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM applications a 
            WHERE a.id = license_keys.app_id 
            AND a.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Users can update their own license keys" ON license_keys;
CREATE POLICY "Users can update their own license keys" ON license_keys
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM applications a 
            WHERE a.id = license_keys.app_id 
            AND a.user_id = auth.uid()
        )
    );

-- =====================================================
-- STEP 9: SAMPLE DATA FOR TESTING
-- =====================================================

-- This section is commented out - uncomment if you want sample data
/*
-- Insert sample application (uncomment if needed)
INSERT INTO applications (
    user_id, 
    app_id, 
    app_secret, 
    app_name, 
    app_description, 
    platform,
    discord_support
) VALUES (
    auth.uid(), -- Current user
    generate_app_id(),
    generate_app_secret(),
    'Sample Desktop App',
    'A sample desktop application for testing',
    'desktop',
    'discord.gg/server'
);
*/

-- =====================================================
-- STEP 10: UPDATE FUNCTIONS FOR NEW COLUMN NAMES
-- =====================================================

-- Update get_license_key_stats function to use current_uses instead of usage_count
CREATE OR REPLACE FUNCTION get_license_key_stats()
RETURNS TABLE(
    total_keys INTEGER,
    active_keys INTEGER,
    expired_keys INTEGER,
    revoked_keys INTEGER,
    total_usage INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*)::INTEGER as total_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_active, TRUE) = TRUE AND COALESCE(is_revoked, FALSE) = FALSE)::INTEGER as active_keys,
        COUNT(*) FILTER (WHERE expires_at IS NOT NULL AND expires_at <= NOW())::INTEGER as expired_keys,
        COUNT(*) FILTER (WHERE COALESCE(is_revoked, FALSE) = TRUE)::INTEGER as revoked_keys,
        COALESCE(SUM(current_uses), 0)::INTEGER as total_usage
    FROM license_keys;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update validate_license_key function to use current_uses instead of usage_count
CREATE OR REPLACE FUNCTION validate_license_key(
    key_to_validate TEXT,
    hardware_id TEXT DEFAULT NULL
)
RETURNS TABLE(
    valid BOOLEAN,
    message TEXT,
    key_data JSONB
) AS $$
DECLARE
    key_record RECORD;
    result_valid BOOLEAN := FALSE;
    result_message TEXT := 'Invalid license key';
    result_data JSONB := '{}'::jsonb;
BEGIN
    -- Get license key record
    SELECT * INTO key_record
    FROM license_keys
    WHERE license_key = key_to_validate;

    IF NOT FOUND THEN
        result_message := 'License key not found';
    ELSE
        -- Check if key is active
        IF COALESCE(key_record.is_revoked, FALSE) = TRUE THEN
            result_message := 'License key has been revoked';
        -- Check expiration
        ELSIF key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW() THEN
            result_message := 'License key has expired';
        -- Check if key is active
        ELSIF COALESCE(key_record.is_active, TRUE) = FALSE THEN
            result_message := 'License key is inactive';
        -- Check hardware binding
        ELSIF hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NOT NULL
              AND key_record.bound_hardware_id != hardware_id THEN
            -- Revoke the key for security
            UPDATE license_keys
            SET is_revoked = TRUE,
                revoked_at = NOW(),
                revoked_reason = 'Hardware ID mismatch detected'
            WHERE id = key_record.id;

            result_message := 'SECURITY VIOLATION: License key has been revoked due to hardware ID mismatch. This indicates potential key sharing or unauthorized usage. Contact support at discord.gg/server if you believe this is an error.';
        -- Check usage limits
        ELSIF key_record.max_uses IS NOT NULL AND key_record.current_uses >= key_record.max_uses THEN
            result_message := 'License key usage limit exceeded';
        ELSE
            -- Key is valid
            result_valid := TRUE;
            result_message := 'License key validated successfully';

            -- Update usage statistics
            UPDATE license_keys
            SET
                current_uses = current_uses + 1,
                last_used_at = NOW(),
                bound_hardware_id = COALESCE(key_record.bound_hardware_id, hardware_id),
                bound_at = CASE
                    WHEN key_record.bound_hardware_id IS NULL AND hardware_id IS NOT NULL
                    THEN NOW()
                    ELSE key_record.bound_at
                END,
                updated_at = NOW()
            WHERE id = key_record.id;

            -- Prepare key data (excluding sensitive info)
            result_data := jsonb_build_object(
                'tier', key_record.tier,
                'expires_at', key_record.expires_at,
                'current_uses', key_record.current_uses + 1,
                'max_uses', key_record.max_uses,
                'is_unlimited', COALESCE(key_record.is_unlimited, FALSE)
            );
        END IF;
    END IF;

    RETURN QUERY SELECT result_valid, result_message, result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for the updated functions
GRANT EXECUTE ON FUNCTION get_license_key_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION validate_license_key(TEXT, TEXT) TO anon, authenticated;

-- Function to create default application for new users
CREATE OR REPLACE FUNCTION create_default_application_for_user()
RETURNS TRIGGER AS $$
DECLARE
    new_app_id UUID;
BEGIN
    -- Create a default application for the new user
    INSERT INTO applications (
        user_id,
        app_id,
        app_secret,
        app_name,
        app_description,
        platform,
        discord_support,
        total_validations,
        successful_validations,
        failed_validations
    ) VALUES (
        NEW.id,
        generate_app_id(),
        generate_app_secret(),
        'My First Application',
        'Default application created automatically',
        'desktop',
        'discord.gg/server',
        0,
        0,
        0
    ) RETURNING id INTO new_app_id;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create default application for new users
DROP TRIGGER IF EXISTS create_default_app_on_user_creation ON auth.users;
CREATE TRIGGER create_default_app_on_user_creation
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION create_default_application_for_user();

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Display summary
DO $$
DECLARE
    app_count INTEGER;
    key_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO app_count FROM applications;
    SELECT COUNT(*) INTO key_count FROM license_keys;
    
    RAISE NOTICE '=== MIGRATION COMPLETED SUCCESSFULLY ===';
    RAISE NOTICE 'Applications created: %', app_count;
    RAISE NOTICE 'License keys migrated: %', key_count;
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Check the applications table for your app credentials';
    RAISE NOTICE '2. Update your .NET applications to use the new app-based config';
    RAISE NOTICE '3. Test license validation with the new system';
    RAISE NOTICE '';
    RAISE NOTICE 'Your app credentials can be found in the applications table:';
    RAISE NOTICE 'SELECT app_id, app_secret, app_name FROM applications;';
END $$;
