-- Verification script for Global Ban System
-- Run this in Supabase SQL editor to check if everything was created correctly

-- 1. Check if global_bans table exists and has correct structure
SELECT 
    'global_bans table structure' as check_type,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'global_bans'
ORDER BY ordinal_position;

-- 2. Check if indexes were created
SELECT 
    'global_bans indexes' as check_type,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'global_bans';

-- 3. Check if functions exist
SELECT 
    'global_bans functions' as check_type,
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_name IN ('is_globally_banned', 'add_global_ban', 'lift_global_ban')
ORDER BY routine_name;

-- 4. Test the is_globally_banned function
SELECT 
    'is_globally_banned function test' as check_type,
    *
FROM is_globally_banned('*************'::inet, 'TEST_HWID_123');

-- 5. Test adding a global ban
SELECT 
    'add_global_ban function test' as check_type,
    add_global_ban(
        '*************'::inet,
        'TEST_HWID_123',
        'Test ban for verification',
        NULL
    ) as ban_id;

-- 6. Test checking the ban we just created
SELECT 
    'check test ban' as check_type,
    *
FROM is_globally_banned('*************'::inet, 'TEST_HWID_123');

-- 7. View the test ban record
SELECT 
    'test ban record' as check_type,
    id,
    ip_address,
    hardware_id,
    ban_reason,
    is_active,
    banned_at
FROM global_bans 
WHERE hardware_id = 'TEST_HWID_123';

-- 8. Test lifting the ban
SELECT 
    'lift_global_ban function test' as check_type,
    lift_global_ban(
        '*************'::inet,
        'TEST_HWID_123',
        NULL,
        'Test ban lifted'
    ) as ban_lifted;

-- 9. Verify the ban was lifted
SELECT 
    'verify ban lifted' as check_type,
    *
FROM is_globally_banned('*************'::inet, 'TEST_HWID_123');

-- 10. Clean up test data
DELETE FROM global_bans WHERE hardware_id = 'TEST_HWID_123';

-- 11. Check if there are any actual global bans in the system
SELECT 
    'actual global bans' as check_type,
    COUNT(*) as total_bans,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_bans
FROM global_bans;

-- 12. Show any existing global bans (for debugging)
SELECT 
    'existing global bans' as check_type,
    ip_address,
    hardware_id,
    ban_reason,
    is_active,
    banned_at
FROM global_bans 
ORDER BY banned_at DESC
LIMIT 10;
