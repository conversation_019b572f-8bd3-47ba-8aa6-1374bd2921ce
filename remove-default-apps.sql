-- =====================================================
-- Remove Default Applications (Optional)
-- =====================================================
-- This script removes any default applications that were created automatically
-- Run this ONLY if you want to clean up existing default apps

-- =====================================================
-- STEP 1: REMOVE DEFAULT APPLICATIONS
-- =====================================================

-- Remove applications with default names (optional - only run if you want to clean up)
-- This will also remove any license keys associated with these applications

-- Uncomment the lines below if you want to remove default applications:

/*
-- Remove default applications and their license keys
DELETE FROM license_keys 
WHERE app_id IN (
    SELECT id FROM applications 
    WHERE app_name IN ('My First Application', 'Legacy Application')
);

DELETE FROM applications 
WHERE app_name IN ('My First Application', 'Legacy Application');
*/

-- =====================================================
-- STEP 2: REMOVE THE DEFAULT APP CREATION FUNCTION
-- =====================================================

-- Remove the function that was used to create default applications
DROP FUNCTION IF EXISTS ensure_user_has_default_application(UUID);

-- =====================================================
-- STEP 3: VERIFICATION
-- =====================================================

-- Show remaining applications
DO $$
DECLARE
    app_count INTEGER;
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO app_count FROM applications;
    SELECT COUNT(*) INTO user_count FROM auth.users;
    
    RAISE NOTICE '=== CLEANUP COMPLETED ===';
    RAISE NOTICE 'Total users: %', user_count;
    RAISE NOTICE 'Remaining applications: %', app_count;
    RAISE NOTICE '';
    
    IF app_count = 0 THEN
        RAISE NOTICE 'All applications removed. Users will start with a clean slate.';
    ELSE
        RAISE NOTICE 'Some applications remain. Check if these are user-created:';
        RAISE NOTICE 'SELECT app_name, platform, created_at FROM applications ORDER BY created_at;';
    END IF;
    
    RAISE NOTICE '';
    RAISE NOTICE 'New users will now start with NO default applications.';
    RAISE NOTICE 'They must manually create their first application.';
END $$;
 