-- =====================================================
-- CUSTOM TIERS SYSTEM MIGRATION
-- =====================================================
-- This migration adds support for custom tiers per application
-- Users can create, edit, and delete their own tier system
-- instead of being limited to basic/premium/enterprise

-- =====================================================
-- STEP 1: CREATE CUSTOM TIERS TABLE
-- =====================================================

-- Create custom tiers table
CREATE TABLE IF NOT EXISTS custom_tiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    app_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    
    -- Tier details
    tier_name VARCHAR(100) NOT NULL,
    tier_description TEXT,
    tier_color VARCHAR(7) DEFAULT '#8B5CF6', -- Hex color code (default purple)
    tier_order INTEGER DEFAULT 0, -- For sorting tiers
    
    -- Tier settings
    is_active BOOLEAN DEFAULT TRUE,
    is_default BOOLEAN DEFAULT FALSE, -- One tier per app can be default
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by_user_id UUID REFERENCES auth.users(id),
    
    -- Constraints
    UNIQUE(app_id, tier_name), -- Tier names must be unique per application
    CHECK (tier_name ~ '^[a-zA-Z0-9_\-\s]+$'), -- Alphanumeric, underscore, dash, space only
    CHECK (length(tier_name) >= 1 AND length(tier_name) <= 100),
    CHECK (tier_color ~ '^#[0-9A-Fa-f]{6}$') -- Valid hex color
);

-- =====================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- =====================================================

-- Index for fast lookups by application
CREATE INDEX IF NOT EXISTS idx_custom_tiers_app_id ON custom_tiers(app_id);

-- Index for ordering tiers
CREATE INDEX IF NOT EXISTS idx_custom_tiers_app_order ON custom_tiers(app_id, tier_order);

-- Index for finding default tier
CREATE INDEX IF NOT EXISTS idx_custom_tiers_default ON custom_tiers(app_id, is_default) WHERE is_default = TRUE;

-- =====================================================
-- STEP 3: CREATE FUNCTIONS FOR TIER MANAGEMENT
-- =====================================================

-- Function to ensure only one default tier per application
CREATE OR REPLACE FUNCTION ensure_single_default_tier()
RETURNS TRIGGER AS $$
BEGIN
    -- If setting a tier as default, unset all other defaults for this app
    IF NEW.is_default = TRUE THEN
        UPDATE custom_tiers 
        SET is_default = FALSE, updated_at = NOW()
        WHERE app_id = NEW.app_id 
        AND id != NEW.id 
        AND is_default = TRUE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for default tier enforcement
DROP TRIGGER IF EXISTS trigger_ensure_single_default_tier ON custom_tiers;
CREATE TRIGGER trigger_ensure_single_default_tier
    BEFORE INSERT OR UPDATE ON custom_tiers
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_default_tier();

-- Function to validate tier deletion
CREATE OR REPLACE FUNCTION validate_tier_deletion()
RETURNS TRIGGER AS $$
DECLARE
    key_count INTEGER;
BEGIN
    -- Check if any license keys are using this tier
    SELECT COUNT(*) INTO key_count
    FROM license_keys
    WHERE app_id = OLD.app_id 
    AND tier = OLD.tier_name;
    
    -- If keys exist with this tier, prevent deletion
    IF key_count > 0 THEN
        RAISE EXCEPTION 'Cannot delete tier "%" because % license key(s) are using it. Please reassign or delete those keys first.', 
            OLD.tier_name, key_count;
    END IF;
    
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for tier deletion validation
DROP TRIGGER IF EXISTS trigger_validate_tier_deletion ON custom_tiers;
CREATE TRIGGER trigger_validate_tier_deletion
    BEFORE DELETE ON custom_tiers
    FOR EACH ROW
    EXECUTE FUNCTION validate_tier_deletion();

-- =====================================================
-- STEP 4: UPDATE LICENSE KEYS TABLE
-- =====================================================

-- Remove the old tier constraint to allow custom tier names
ALTER TABLE license_keys
DROP CONSTRAINT IF EXISTS license_keys_tier_check;

-- Allow NULL tiers (applications can exist without tier systems)
-- Only check length if tier is provided
ALTER TABLE license_keys
ADD CONSTRAINT license_keys_tier_check
CHECK (tier IS NULL OR length(trim(tier)) > 0);

-- =====================================================
-- STEP 5: CREATE VIEWS FOR EASY QUERYING
-- =====================================================

-- View for tier statistics
CREATE OR REPLACE VIEW tier_usage_stats AS
SELECT 
    ct.id as tier_id,
    ct.app_id,
    ct.tier_name,
    ct.tier_description,
    ct.tier_color,
    ct.tier_order,
    ct.is_default,
    ct.is_active,
    COUNT(lk.id) as total_keys,
    COUNT(CASE WHEN lk.is_active = TRUE AND lk.is_revoked = FALSE THEN 1 END) as active_keys,
    COUNT(CASE WHEN lk.is_revoked = TRUE THEN 1 END) as revoked_keys,
    COUNT(CASE WHEN lk.expires_at IS NOT NULL AND lk.expires_at < NOW() THEN 1 END) as expired_keys
FROM custom_tiers ct
LEFT JOIN license_keys lk ON ct.app_id = lk.app_id AND ct.tier_name = lk.tier
GROUP BY ct.id, ct.app_id, ct.tier_name, ct.tier_description, ct.tier_color, ct.tier_order, ct.is_default, ct.is_active
ORDER BY ct.app_id, ct.tier_order, ct.tier_name;

-- =====================================================
-- STEP 6: ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on custom_tiers table
ALTER TABLE custom_tiers ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see tiers for their own applications
CREATE POLICY "Users can view their own application tiers" ON custom_tiers
    FOR SELECT USING (
        app_id IN (
            SELECT id FROM applications WHERE user_id = auth.uid()
        )
    );

-- Policy: Users can only insert tiers for their own applications
CREATE POLICY "Users can insert tiers for their own applications" ON custom_tiers
    FOR INSERT WITH CHECK (
        app_id IN (
            SELECT id FROM applications WHERE user_id = auth.uid()
        )
    );

-- Policy: Users can only update tiers for their own applications
CREATE POLICY "Users can update their own application tiers" ON custom_tiers
    FOR UPDATE USING (
        app_id IN (
            SELECT id FROM applications WHERE user_id = auth.uid()
        )
    );

-- Policy: Users can only delete tiers for their own applications
CREATE POLICY "Users can delete their own application tiers" ON custom_tiers
    FOR DELETE USING (
        app_id IN (
            SELECT id FROM applications WHERE user_id = auth.uid()
        )
    );

-- =====================================================
-- STEP 7: HELPER FUNCTIONS
-- =====================================================

-- Function to get default tier for an application
CREATE OR REPLACE FUNCTION get_default_tier(p_app_id UUID)
RETURNS TEXT AS $$
DECLARE
    default_tier_name TEXT;
BEGIN
    SELECT tier_name INTO default_tier_name
    FROM custom_tiers
    WHERE app_id = p_app_id
    AND is_default = TRUE
    AND is_active = TRUE
    LIMIT 1;

    -- If no default tier found, return the first active tier
    IF default_tier_name IS NULL THEN
        SELECT tier_name INTO default_tier_name
        FROM custom_tiers
        WHERE app_id = p_app_id
        AND is_active = TRUE
        ORDER BY tier_order, tier_name
        LIMIT 1;
    END IF;

    -- Return NULL if no tiers exist (applications can have no tiers)
    RETURN default_tier_name;
END;
$$ LANGUAGE plpgsql;

-- Function to create a new tier
CREATE OR REPLACE FUNCTION create_custom_tier(
    p_app_id UUID,
    p_tier_name VARCHAR(100),
    p_tier_description TEXT DEFAULT NULL,
    p_tier_color VARCHAR(7) DEFAULT '#8B5CF6',
    p_tier_order INTEGER DEFAULT 0,
    p_is_default BOOLEAN DEFAULT FALSE,
    p_created_by_user_id UUID DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_tier_id UUID;
    tier_count INTEGER;
BEGIN
    -- Check if user owns the application
    IF NOT EXISTS (
        SELECT 1 FROM applications 
        WHERE id = p_app_id 
        AND user_id = COALESCE(p_created_by_user_id, auth.uid())
    ) THEN
        RAISE EXCEPTION 'Application not found or access denied';
    END IF;
    
    -- Check if tier name already exists for this app
    IF EXISTS (
        SELECT 1 FROM custom_tiers 
        WHERE app_id = p_app_id 
        AND tier_name = p_tier_name
    ) THEN
        RAISE EXCEPTION 'Tier name "%" already exists for this application', p_tier_name;
    END IF;
    
    -- Get current tier count for this app
    SELECT COUNT(*) INTO tier_count
    FROM custom_tiers
    WHERE app_id = p_app_id;

    -- Don't automatically make first tier default - let user decide
    
    -- Insert the new tier
    INSERT INTO custom_tiers (
        app_id,
        tier_name,
        tier_description,
        tier_color,
        tier_order,
        is_default,
        created_by_user_id
    ) VALUES (
        p_app_id,
        p_tier_name,
        p_tier_description,
        p_tier_color,
        COALESCE(p_tier_order, tier_count),
        p_is_default,
        COALESCE(p_created_by_user_id, auth.uid())
    ) RETURNING id INTO new_tier_id;
    
    RETURN new_tier_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 8: MIGRATION HELPER FUNCTIONS
-- =====================================================

-- Function to migrate existing tiers for an application
CREATE OR REPLACE FUNCTION migrate_existing_tiers_for_app(p_app_id UUID)
RETURNS INTEGER AS $$
DECLARE
    app_user_id UUID;
    tier_count INTEGER := 0;
    existing_tiers TEXT[];
BEGIN
    -- Get the application owner
    SELECT user_id INTO app_user_id
    FROM applications
    WHERE id = p_app_id;

    IF app_user_id IS NULL THEN
        RAISE EXCEPTION 'Application not found: %', p_app_id;
    END IF;

    -- Get unique tiers used in license keys for this app
    SELECT ARRAY_AGG(DISTINCT tier) INTO existing_tiers
    FROM license_keys
    WHERE app_id = p_app_id
    AND tier IS NOT NULL;

    -- Create custom tiers for each existing tier
    IF existing_tiers IS NOT NULL THEN
        FOR i IN 1..array_length(existing_tiers, 1) LOOP
            -- Skip if tier already exists in custom_tiers
            IF NOT EXISTS (
                SELECT 1 FROM custom_tiers
                WHERE app_id = p_app_id
                AND tier_name = existing_tiers[i]
            ) THEN
                -- Determine color based on tier name
                DECLARE
                    tier_color VARCHAR(7) := '#8B5CF6'; -- Default purple
                BEGIN
                    CASE LOWER(existing_tiers[i])
                        WHEN 'basic' THEN tier_color := '#3B82F6'; -- Blue
                        WHEN 'premium' THEN tier_color := '#8B5CF6'; -- Purple
                        WHEN 'enterprise' THEN tier_color := '#6366F1'; -- Indigo
                        ELSE tier_color := '#8B5CF6'; -- Default purple
                    END CASE;

                    -- Create the tier
                    PERFORM create_custom_tier(
                        p_app_id,
                        existing_tiers[i],
                        CASE LOWER(existing_tiers[i])
                            WHEN 'basic' THEN 'Basic tier with standard features'
                            WHEN 'premium' THEN 'Premium tier with enhanced features'
                            WHEN 'enterprise' THEN 'Enterprise tier with full features'
                            ELSE 'Migrated from existing license keys'
                        END,
                        tier_color,
                        i - 1, -- Order starting from 0
                        i = 1, -- First tier is default
                        app_user_id
                    );

                    tier_count := tier_count + 1;
                END;
            END IF;
        END LOOP;
    END IF;

    RETURN tier_count;
END;
$$ LANGUAGE plpgsql;

-- Function to migrate all existing applications
CREATE OR REPLACE FUNCTION migrate_all_existing_tiers()
RETURNS TABLE(app_id UUID, app_name TEXT, migrated_tiers INTEGER) AS $$
DECLARE
    app_record RECORD;
    migrated_count INTEGER;
BEGIN
    FOR app_record IN
        SELECT id, app_name FROM applications WHERE is_active = TRUE
    LOOP
        migrated_count := migrate_existing_tiers_for_app(app_record.id);

        RETURN QUERY SELECT
            app_record.id,
            app_record.app_name,
            migrated_count;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 9: COMMENTS AND DOCUMENTATION
-- =====================================================

COMMENT ON TABLE custom_tiers IS 'Custom tier definitions per application. Allows users to create their own tier system instead of using hardcoded basic/premium/enterprise.';
COMMENT ON COLUMN custom_tiers.tier_name IS 'Name of the tier (must be unique per application)';
COMMENT ON COLUMN custom_tiers.tier_description IS 'Optional description of what this tier includes';
COMMENT ON COLUMN custom_tiers.tier_color IS 'Hex color code for UI display (e.g., #8B5CF6)';
COMMENT ON COLUMN custom_tiers.tier_order IS 'Sort order for displaying tiers (lower numbers first)';
COMMENT ON COLUMN custom_tiers.is_default IS 'Whether this tier is the default for new license keys';
COMMENT ON FUNCTION create_custom_tier IS 'Creates a new custom tier with validation and automatic default handling';
COMMENT ON FUNCTION get_default_tier IS 'Returns the default tier name for an application';
COMMENT ON FUNCTION migrate_existing_tiers_for_app IS 'Migrates existing license key tiers to custom tiers for a specific application';

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- To run the migration for existing applications, execute:
-- SELECT * FROM migrate_all_existing_tiers();

-- To create a new tier manually:
-- SELECT create_custom_tier(
--     'your-app-id'::UUID,
--     'Custom Tier Name',
--     'Description of this tier',
--     '#FF6B6B',
--     0,
--     true
-- );
