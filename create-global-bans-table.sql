-- Create Global Bans Table for IP/HWID Combination Bans
-- This table tracks globally banned IP/HWID combinations that prevent
-- users from using ANY license key, not just specific device-license combinations

-- Create the global_bans table
CREATE TABLE IF NOT EXISTS global_bans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Ban identifiers
    ip_address INET NOT NULL,
    hardware_id TEXT NOT NULL,
    
    -- Ban metadata
    ban_reason TEXT NOT NULL DEFAULT 'Violation of terms of service',
    banned_by_admin_id UUID, -- References the admin who created the ban
    banned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Optional expiration (NULL = permanent ban)
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Status tracking
    is_active BOOLEAN DEFAULT TRUE,
    lifted_at TIMESTAMP WITH TIME ZONE,
    lifted_by_admin_id UUID,
    lift_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique combination of IP and HWID for active bans
    UNIQUE(ip_address, hardware_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_global_bans_ip_hwid ON global_bans(ip_address, hardware_id);
CREATE INDEX IF NOT EXISTS idx_global_bans_active ON global_bans(is_active) WHERE is_active = TRUE;
CREATE INDEX IF NOT EXISTS idx_global_bans_expires ON global_bans(expires_at) WHERE expires_at IS NOT NULL;

-- Add foreign key constraints if auth.users exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'auth' AND table_name = 'users') THEN
        ALTER TABLE global_bans 
        ADD CONSTRAINT fk_global_bans_banned_by 
        FOREIGN KEY (banned_by_admin_id) REFERENCES auth.users(id) ON DELETE SET NULL;
        
        ALTER TABLE global_bans 
        ADD CONSTRAINT fk_global_bans_lifted_by 
        FOREIGN KEY (lifted_by_admin_id) REFERENCES auth.users(id) ON DELETE SET NULL;
        
        RAISE NOTICE '✓ Added foreign key constraints to auth.users';
    ELSE
        RAISE NOTICE '⚠ Skipped foreign key constraints - auth.users not found';
    END IF;
END $$;

-- Create function to check if IP/HWID combination is globally banned
CREATE OR REPLACE FUNCTION is_globally_banned(
    check_ip_address INET,
    check_hardware_id TEXT
)
RETURNS TABLE(
    banned BOOLEAN,
    ban_reason TEXT,
    banned_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    ban_record RECORD;
BEGIN
    -- Look for active global ban for this IP/HWID combination
    SELECT * INTO ban_record
    FROM global_bans
    WHERE ip_address = check_ip_address
      AND hardware_id = check_hardware_id
      AND is_active = TRUE
      AND (expires_at IS NULL OR expires_at > NOW());
    
    IF FOUND THEN
        -- Return ban details
        RETURN QUERY SELECT 
            TRUE as banned,
            ban_record.ban_reason,
            ban_record.banned_at,
            ban_record.expires_at;
    ELSE
        -- Not banned
        RETURN QUERY SELECT 
            FALSE as banned,
            NULL::TEXT as ban_reason,
            NULL::TIMESTAMP WITH TIME ZONE as banned_at,
            NULL::TIMESTAMP WITH TIME ZONE as expires_at;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to add a global ban
CREATE OR REPLACE FUNCTION add_global_ban(
    ban_ip_address INET,
    ban_hardware_id TEXT,
    ban_reason TEXT DEFAULT 'Violation of terms of service',
    banned_by_admin UUID DEFAULT NULL,
    ban_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    new_ban_id UUID;
BEGIN
    -- Insert the global ban
    INSERT INTO global_bans (
        ip_address,
        hardware_id,
        ban_reason,
        banned_by_admin_id,
        expires_at
    ) VALUES (
        ban_ip_address,
        ban_hardware_id,
        ban_reason,
        banned_by_admin,
        ban_expires_at
    )
    ON CONFLICT (ip_address, hardware_id)
    DO UPDATE SET
        ban_reason = EXCLUDED.ban_reason,
        banned_by_admin_id = EXCLUDED.banned_by_admin_id,
        expires_at = EXCLUDED.expires_at,
        is_active = TRUE,
        lifted_at = NULL,
        lifted_by_admin_id = NULL,
        lift_reason = NULL,
        updated_at = NOW()
    RETURNING id INTO new_ban_id;

    RETURN new_ban_id;
END;
$$ LANGUAGE plpgsql;

-- Create function to lift a global ban
CREATE OR REPLACE FUNCTION lift_global_ban(
    ban_ip_address INET,
    ban_hardware_id TEXT,
    lifted_by_admin UUID DEFAULT NULL,
    lift_reason TEXT DEFAULT 'Ban lifted by administrator'
)
RETURNS BOOLEAN AS $$
DECLARE
    ban_found BOOLEAN := FALSE;
    rows_affected INTEGER;
BEGIN
    -- Update the ban to inactive
    UPDATE global_bans
    SET
        is_active = FALSE,
        lifted_at = NOW(),
        lifted_by_admin_id = lifted_by_admin,
        lift_reason = lift_reason,
        updated_at = NOW()
    WHERE ip_address = ban_ip_address
      AND hardware_id = ban_hardware_id
      AND is_active = TRUE;

    GET DIAGNOSTICS rows_affected = ROW_COUNT;
    ban_found := rows_affected > 0;
    RETURN ban_found;
END;
$$ LANGUAGE plpgsql;

-- Verify the table was created successfully
SELECT
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'global_bans'
ORDER BY ordinal_position;
