-- Add Reseller Features to KeyAuth System
-- Run this in Supabase SQL Editor

-- =====================================================
-- STEP 1: CREATE USER PROFILES TABLE FOR RESELLER DATA
-- =====================================================

-- Create user profiles table to store reseller information
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

    -- Reseller fields
    user_role VARCHAR(50) DEFAULT 'user' CHECK (user_role IN ('user', 'reseller', 'admin')),
    reseller_tier VARCHAR(50) DEFAULT 'basic' CHECK (reseller_tier IN ('basic', 'premium', 'enterprise')),
    commission_rate DECIMAL(5,2) DEFAULT 0.00, -- Percentage commission (e.g., 15.50 for 15.5%)
    parent_reseller_id UUID REFERENCES auth.users(id),
    reseller_code VARCHAR(20) UNIQUE,
    is_active_reseller BOOLEAN DEFAULT FALSE,
    reseller_joined_at TIMESTAMP WITH TIME ZONE,

    -- Additional profile data
    display_name VARCHAR(100),
    company_name VARCHAR(100),
    phone VARCHAR(20),

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    UNIQUE(user_id)
);

-- Create indexes for reseller lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_reseller_code ON user_profiles(reseller_code);
CREATE INDEX IF NOT EXISTS idx_user_profiles_parent_reseller ON user_profiles(parent_reseller_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(user_role);

-- =====================================================
-- STEP 2: CREATE RESELLER SALES TRACKING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS reseller_sales (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    reseller_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    license_key_id UUID NOT NULL REFERENCES license_keys(id) ON DELETE CASCADE,
    application_id UUID NOT NULL REFERENCES applications(id) ON DELETE CASCADE,
    
    -- Sale details
    sale_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    commission_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    commission_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    
    -- Customer information (optional)
    customer_email VARCHAR(255),
    customer_reference VARCHAR(100),
    
    -- Sale metadata
    sale_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    payment_status VARCHAR(50) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    payment_reference VARCHAR(100),
    
    -- Tracking
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(license_key_id) -- Each license key can only have one sale record
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_reseller_sales_reseller_id ON reseller_sales(reseller_id);
CREATE INDEX IF NOT EXISTS idx_reseller_sales_date ON reseller_sales(sale_date);
CREATE INDEX IF NOT EXISTS idx_reseller_sales_status ON reseller_sales(payment_status);

-- =====================================================
-- STEP 3: CREATE RESELLER COMMISSION TRACKING
-- =====================================================

CREATE TABLE IF NOT EXISTS reseller_commissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    reseller_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    sale_id UUID NOT NULL REFERENCES reseller_sales(id) ON DELETE CASCADE,
    
    -- Commission details
    commission_amount DECIMAL(10,2) NOT NULL,
    commission_rate DECIMAL(5,2) NOT NULL,
    base_amount DECIMAL(10,2) NOT NULL,
    
    -- Payout tracking
    payout_status VARCHAR(50) DEFAULT 'pending' CHECK (payout_status IN ('pending', 'paid', 'cancelled')),
    payout_date TIMESTAMP WITH TIME ZONE,
    payout_reference VARCHAR(100),
    payout_method VARCHAR(50), -- paypal, bank_transfer, crypto, etc.
    
    -- Period tracking
    commission_period VARCHAR(20), -- e.g., "2024-01" for monthly payouts
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_reseller_commissions_reseller_id ON reseller_commissions(reseller_id);
CREATE INDEX IF NOT EXISTS idx_reseller_commissions_period ON reseller_commissions(commission_period);
CREATE INDEX IF NOT EXISTS idx_reseller_commissions_status ON reseller_commissions(payout_status);

-- =====================================================
-- STEP 4: ADD RESELLER TRACKING TO LICENSE KEYS
-- =====================================================

-- Add reseller tracking to license keys
ALTER TABLE license_keys 
ADD COLUMN IF NOT EXISTS created_by_reseller_id UUID REFERENCES auth.users(id),
ADD COLUMN IF NOT EXISTS reseller_code VARCHAR(20),
ADD COLUMN IF NOT EXISTS sale_price DECIMAL(10,2) DEFAULT 0.00,
ADD COLUMN IF NOT EXISTS is_sold BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS sold_at TIMESTAMP WITH TIME ZONE;

-- Create index for reseller lookups
CREATE INDEX IF NOT EXISTS idx_license_keys_reseller ON license_keys(created_by_reseller_id);
CREATE INDEX IF NOT EXISTS idx_license_keys_reseller_code ON license_keys(reseller_code);

-- =====================================================
-- STEP 5: CREATE RESELLER PERFORMANCE VIEW
-- =====================================================

CREATE OR REPLACE VIEW reseller_performance AS
SELECT
    u.id as reseller_id,
    u.email as reseller_email,
    up.reseller_code,
    up.reseller_tier,
    up.commission_rate,
    up.reseller_joined_at,
    
    -- Sales metrics
    COUNT(rs.id) as total_sales,
    COALESCE(SUM(rs.sale_price), 0) as total_revenue,
    COALESCE(SUM(rs.commission_amount), 0) as total_commissions,
    
    -- License key metrics
    COUNT(lk.id) as total_keys_created,
    COUNT(CASE WHEN lk.is_sold THEN 1 END) as keys_sold,
    COUNT(CASE WHEN lk.is_active AND NOT lk.is_revoked THEN 1 END) as active_keys,
    
    -- Performance metrics
    CASE 
        WHEN COUNT(lk.id) > 0 THEN 
            ROUND((COUNT(CASE WHEN lk.is_sold THEN 1 END)::DECIMAL / COUNT(lk.id)) * 100, 2)
        ELSE 0 
    END as conversion_rate,
    
    -- Recent activity (last 30 days)
    COUNT(CASE WHEN rs.sale_date >= NOW() - INTERVAL '30 days' THEN 1 END) as recent_sales_30d,
    COALESCE(SUM(CASE WHEN rs.sale_date >= NOW() - INTERVAL '30 days' THEN rs.sale_price ELSE 0 END), 0) as recent_revenue_30d
    
FROM auth.users u
JOIN user_profiles up ON u.id = up.user_id
LEFT JOIN reseller_sales rs ON u.id = rs.reseller_id
LEFT JOIN license_keys lk ON u.id = lk.created_by_reseller_id
WHERE up.user_role = 'reseller' AND up.is_active_reseller = TRUE
GROUP BY u.id, u.email, up.reseller_code, up.reseller_tier, up.commission_rate, up.reseller_joined_at;

-- =====================================================
-- STEP 6: CREATE HELPER FUNCTIONS
-- =====================================================

-- Function to generate unique reseller code
CREATE OR REPLACE FUNCTION generate_reseller_code()
RETURNS TEXT AS $$
DECLARE
    code TEXT;
    exists BOOLEAN;
BEGIN
    LOOP
        -- Generate 8-character alphanumeric code
        code := upper(substring(md5(random()::text) from 1 for 8));
        
        -- Check if code already exists
        SELECT EXISTS(SELECT 1 FROM auth.users WHERE reseller_code = code) INTO exists;
        
        -- If code doesn't exist, return it
        IF NOT exists THEN
            RETURN code;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate commission
CREATE OR REPLACE FUNCTION calculate_commission(
    sale_amount DECIMAL,
    commission_rate DECIMAL
)
RETURNS DECIMAL AS $$
BEGIN
    RETURN ROUND((sale_amount * commission_rate / 100), 2);
END;
$$ LANGUAGE plpgsql;

-- Function to create reseller sale record
CREATE OR REPLACE FUNCTION create_reseller_sale(
    p_reseller_id UUID,
    p_license_key_id UUID,
    p_sale_price DECIMAL,
    p_customer_email VARCHAR DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    sale_id UUID;
    app_id UUID;
    commission_rate DECIMAL;
    commission_amount DECIMAL;
BEGIN
    -- Get application ID from license key
    SELECT lk.app_id INTO app_id 
    FROM license_keys lk 
    WHERE lk.id = p_license_key_id;
    
    -- Get reseller commission rate
    SELECT up.commission_rate INTO commission_rate
    FROM user_profiles up
    WHERE up.user_id = p_reseller_id;
    
    -- Calculate commission
    commission_amount := calculate_commission(p_sale_price, commission_rate);
    
    -- Create sale record
    INSERT INTO reseller_sales (
        reseller_id,
        license_key_id,
        application_id,
        sale_price,
        commission_rate,
        commission_amount,
        customer_email
    ) VALUES (
        p_reseller_id,
        p_license_key_id,
        app_id,
        p_sale_price,
        commission_rate,
        commission_amount,
        p_customer_email
    ) RETURNING id INTO sale_id;
    
    -- Update license key as sold
    UPDATE license_keys 
    SET is_sold = TRUE, 
        sold_at = NOW(),
        sale_price = p_sale_price
    WHERE id = p_license_key_id;
    
    RETURN sale_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 7: CREATE TRIGGERS FOR AUTOMATIC UPDATES
-- =====================================================

-- Trigger to update reseller sales updated_at
CREATE OR REPLACE FUNCTION update_reseller_sales_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_reseller_sales_updated_at
    BEFORE UPDATE ON reseller_sales
    FOR EACH ROW
    EXECUTE FUNCTION update_reseller_sales_updated_at();

-- Trigger to update reseller commissions updated_at
CREATE OR REPLACE FUNCTION update_reseller_commissions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_reseller_commissions_updated_at
    BEFORE UPDATE ON reseller_commissions
    FOR EACH ROW
    EXECUTE FUNCTION update_reseller_commissions_updated_at();

-- =====================================================
-- STEP 8: ENABLE RLS AND CREATE POLICIES
-- =====================================================

-- Enable RLS on new tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE reseller_sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE reseller_commissions ENABLE ROW LEVEL SECURITY;

-- User profiles policies
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Service role can access all profiles" ON user_profiles
    FOR ALL USING (auth.role() = 'service_role');

-- Reseller sales policies
CREATE POLICY "Resellers can view their own sales" ON reseller_sales
    FOR SELECT USING (auth.uid() = reseller_id);

CREATE POLICY "Service role can access all sales" ON reseller_sales
    FOR ALL USING (auth.role() = 'service_role');

-- Reseller commissions policies
CREATE POLICY "Resellers can view their own commissions" ON reseller_commissions
    FOR SELECT USING (auth.uid() = reseller_id);

CREATE POLICY "Service role can access all commissions" ON reseller_commissions
    FOR ALL USING (auth.role() = 'service_role');

-- =====================================================
-- STEP 9: GRANT PERMISSIONS
-- =====================================================

-- Grant permissions for reseller tables
GRANT ALL ON user_profiles TO service_role;
GRANT ALL ON reseller_sales TO service_role;
GRANT ALL ON reseller_commissions TO service_role;
GRANT SELECT ON reseller_performance TO service_role;

GRANT SELECT, INSERT, UPDATE ON user_profiles TO authenticated;
GRANT SELECT, INSERT, UPDATE ON reseller_sales TO authenticated;
GRANT SELECT ON reseller_commissions TO authenticated;
GRANT SELECT ON reseller_performance TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION generate_reseller_code() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION calculate_commission(DECIMAL, DECIMAL) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION create_reseller_sale(UUID, UUID, DECIMAL, VARCHAR) TO authenticated, service_role;
