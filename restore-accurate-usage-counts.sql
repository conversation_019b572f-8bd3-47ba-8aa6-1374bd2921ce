-- Restore accurate usage counts from validation logs
-- Run this in Supabase SQL editor to rebuild correct usage statistics

-- Step 1: Update license_keys.current_uses based on successful validation logs
UPDATE license_keys 
SET current_uses = (
    SELECT COUNT(*) 
    FROM license_validation_logs lvl 
    WHERE lvl.license_key_id = license_keys.id 
      AND lvl.success = true
);

-- Step 2: Update user_license_keys.total_uses based on successful validation logs per user per key
UPDATE user_license_keys 
SET total_uses = (
    SELECT COUNT(*) 
    FROM license_validation_logs lvl 
    WHERE lvl.hardware_device_id = user_license_keys.hardware_device_id 
      AND lvl.license_key_id = user_license_keys.license_key_id 
      AND lvl.success = true
);

-- Step 3: Update last_used_at timestamps based on most recent successful validation
UPDATE user_license_keys 
SET last_used_at = (
    SELECT MAX(lvl.created_at) 
    FROM license_validation_logs lvl 
    WHERE lvl.hardware_device_id = user_license_keys.hardware_device_id 
      AND lvl.license_key_id = user_license_keys.license_key_id 
      AND lvl.success = true
)
WHERE EXISTS (
    SELECT 1 
    FROM license_validation_logs lvl 
    WHERE lvl.hardware_device_id = user_license_keys.hardware_device_id 
      AND lvl.license_key_id = user_license_keys.license_key_id 
      AND lvl.success = true
);

-- Step 4: Update first_used_at timestamps based on earliest successful validation
UPDATE user_license_keys 
SET first_used_at = (
    SELECT MIN(lvl.created_at) 
    FROM license_validation_logs lvl 
    WHERE lvl.hardware_device_id = user_license_keys.hardware_device_id 
      AND lvl.license_key_id = user_license_keys.license_key_id 
      AND lvl.success = true
)
WHERE EXISTS (
    SELECT 1 
    FROM license_validation_logs lvl 
    WHERE lvl.hardware_device_id = user_license_keys.hardware_device_id 
      AND lvl.license_key_id = user_license_keys.license_key_id 
      AND lvl.success = true
);

-- Step 5: Remove any user_license_keys records that have 0 usage (no successful validations)
DELETE FROM user_license_keys 
WHERE total_uses = 0;

-- Step 6: Ensure primary keys are set correctly (most recently used key should be primary)
-- First, remove all primary flags
UPDATE user_license_keys SET is_primary = FALSE;

-- Then set the most recently used key as primary for each user
UPDATE user_license_keys 
SET is_primary = TRUE 
WHERE (hardware_device_id, last_used_at) IN (
    SELECT hardware_device_id, MAX(last_used_at)
    FROM user_license_keys 
    GROUP BY hardware_device_id
);

-- Step 7: Update hardware_devices to point to the primary license key
UPDATE hardware_devices 
SET license_key_id = (
    SELECT ulk.license_key_id 
    FROM user_license_keys ulk 
    WHERE ulk.hardware_device_id = hardware_devices.id 
      AND ulk.is_primary = true
    LIMIT 1
)
WHERE EXISTS (
    SELECT 1 
    FROM user_license_keys ulk 
    WHERE ulk.hardware_device_id = hardware_devices.id 
      AND ulk.is_primary = true
);

-- Verification queries (optional - run these to check the results)
-- SELECT 'License Keys Usage Summary' as info;
-- SELECT license_key, current_uses FROM license_keys WHERE current_uses > 0 ORDER BY current_uses DESC;

-- SELECT 'User License Keys Summary' as info;
-- SELECT ulk.*, lk.license_key 
-- FROM user_license_keys ulk 
-- JOIN license_keys lk ON ulk.license_key_id = lk.id 
-- WHERE ulk.total_uses > 0 
-- ORDER BY ulk.hardware_device_id, ulk.last_used_at DESC;
