    -- =====================================================
-- KeyAuth Database Rollback: App-Based System
-- =====================================================
-- This script rolls back the app-based system migration
-- WARNING: This will remove all app-based data and cannot be undone!

-- =====================================================
-- STEP 1: BACKUP IMPORTANT DATA (OPTIONAL)
-- =====================================================

-- Uncomment these lines if you want to backup data before rollback
/*
CREATE TABLE IF NOT EXISTS backup_applications AS SELECT * FROM applications;
CREATE TABLE IF NOT EXISTS backup_license_validation_logs AS SELECT * FROM license_validation_logs;
CREATE TABLE IF NOT EXISTS backup_hardware_devices AS SELECT * FROM hardware_devices;
CREATE TABLE IF NOT EXISTS backup_api_usage_stats AS SELECT * FROM api_usage_stats;
*/

-- =====================================================
-- STEP 2: DROP NEW TABLES
-- =====================================================

-- Drop views first
DROP VIEW IF EXISTS app_dashboard_view;
DROP VIEW IF EXISTS license_key_management_view;

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS api_usage_stats;
DROP TABLE IF EXISTS hardware_devices;
DROP TABLE IF EXISTS license_validation_logs;
DROP TABLE IF EXISTS applications CASCADE;

-- =====================================================
-- STEP 3: REMOVE ADDED COLUMNS FROM EXISTING TABLES
-- =====================================================

-- Remove columns added to license_keys table
ALTER TABLE license_keys 
DROP COLUMN IF EXISTS app_id,
DROP COLUMN IF EXISTS tier,
DROP COLUMN IF EXISTS is_unlimited,
DROP COLUMN IF EXISTS bound_hardware_id,
DROP COLUMN IF EXISTS bound_at,
DROP COLUMN IF EXISTS created_by_user_id,
DROP COLUMN IF EXISTS notes,
DROP COLUMN IF EXISTS is_revoked,
DROP COLUMN IF EXISTS revoked_at,
DROP COLUMN IF EXISTS revoked_reason;

-- Rename current_uses back to usage_count if it was renamed
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'license_keys' AND column_name = 'current_uses') THEN
        ALTER TABLE license_keys RENAME COLUMN current_uses TO usage_count;
    END IF;
END $$;

-- =====================================================
-- STEP 4: DROP HELPER FUNCTIONS
-- =====================================================

DROP FUNCTION IF EXISTS generate_app_id();
DROP FUNCTION IF EXISTS generate_app_secret();
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- =====================================================
-- STEP 5: DROP INDEXES
-- =====================================================

-- Drop indexes that were created for the new system
DROP INDEX IF EXISTS idx_applications_user_id;
DROP INDEX IF EXISTS idx_applications_app_id;
DROP INDEX IF EXISTS idx_applications_app_secret;
DROP INDEX IF EXISTS idx_applications_created_at;
DROP INDEX IF EXISTS idx_applications_is_active;

DROP INDEX IF EXISTS idx_license_keys_app_id;
DROP INDEX IF EXISTS idx_license_keys_bound_hardware_id;
DROP INDEX IF EXISTS idx_license_keys_tier;
DROP INDEX IF EXISTS idx_license_keys_is_revoked;
DROP INDEX IF EXISTS idx_license_keys_status;

DROP INDEX IF EXISTS idx_validation_logs_app_id;
DROP INDEX IF EXISTS idx_validation_logs_license_key_id;
DROP INDEX IF EXISTS idx_validation_logs_created_at;
DROP INDEX IF EXISTS idx_validation_logs_hardware_id;
DROP INDEX IF EXISTS idx_validation_logs_validation_result;
DROP INDEX IF EXISTS idx_validation_logs_license_key;

DROP INDEX IF EXISTS idx_hardware_devices_license_key_id;
DROP INDEX IF EXISTS idx_hardware_devices_hardware_id;
DROP INDEX IF EXISTS idx_hardware_devices_last_seen;
DROP INDEX IF EXISTS idx_hardware_devices_is_active;

DROP INDEX IF EXISTS idx_api_usage_stats_app_id_date;
DROP INDEX IF EXISTS idx_api_usage_stats_endpoint;

-- =====================================================
-- STEP 6: RESTORE ORIGINAL RLS POLICIES
-- =====================================================

-- Remove app-based policies and restore original ones
DROP POLICY IF EXISTS "Users can view their own applications" ON applications;
DROP POLICY IF EXISTS "Users can insert their own applications" ON applications;
DROP POLICY IF EXISTS "Users can update their own applications" ON applications;
DROP POLICY IF EXISTS "Users can delete their own applications" ON applications;

-- Restore original license keys policies (adjust as needed for your original setup)
DROP POLICY IF EXISTS "Users can view their own license keys" ON license_keys;
DROP POLICY IF EXISTS "Users can insert license keys for their apps" ON license_keys;
DROP POLICY IF EXISTS "Users can update their own license keys" ON license_keys;

-- Recreate original policies (uncomment and modify as needed)
/*
CREATE POLICY "Users can view their own license keys" ON license_keys
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own license keys" ON license_keys
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own license keys" ON license_keys
    FOR UPDATE USING (auth.uid() = user_id);
*/

-- =====================================================
-- STEP 7: CLEAN UP TRIGGERS
-- =====================================================

DROP TRIGGER IF EXISTS update_applications_updated_at ON applications;
DROP TRIGGER IF EXISTS update_license_keys_updated_at ON license_keys;

-- =====================================================
-- ROLLBACK COMPLETE
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=== ROLLBACK COMPLETED ===';
    RAISE NOTICE 'The app-based system has been removed.';
    RAISE NOTICE 'Your database has been restored to its previous state.';
    RAISE NOTICE '';
    RAISE NOTICE 'Note: You may need to:';
    RAISE NOTICE '1. Restore any custom RLS policies';
    RAISE NOTICE '2. Update your applications to use the old configuration';
    RAISE NOTICE '3. Verify that your existing license keys still work';
END $$;
