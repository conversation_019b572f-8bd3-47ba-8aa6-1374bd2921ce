-- Migration to add blur_analytics column to user_preferences table
-- Run this script if you already have an existing user_preferences table

-- Step 1: Add the new blur_analytics column with default value FALSE
ALTER TABLE user_preferences
ADD COLUMN IF NOT EXISTS blur_analytics BOOLEAN DEFAULT FALSE;

-- Step 2: Update any existing records to have blur_analytics = FALSE (default)
UPDATE user_preferences 
SET blur_analytics = FALSE 
WHERE blur_analytics IS NULL;

-- Step 3: Verify the column was added
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'user_preferences' 
            AND column_name = 'blur_analytics'
        ) 
        THEN 'blur_analytics column successfully added' 
        ELSE 'blur_analytics column MISSING - migration failed'
    END as migration_status;

-- Step 4: Show current table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_preferences'
ORDER BY ordinal_position;
