-- Test script to verify global ban functions work correctly
-- Run this in Supabase SQL editor to debug the unban issue

-- Step 1: Check if the functions exist
SELECT 
    'Function Check' as test_step,
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_name IN ('is_globally_banned', 'add_global_ban', 'lift_global_ban')
ORDER BY routine_name;

-- Step 2: Check if global_bans table exists and has correct structure
SELECT 
    'Table Structure' as test_step,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'global_bans'
ORDER BY ordinal_position;

-- Step 3: Test the functions with sample data
DO $$
DECLARE
    test_ip INET := '*************'::inet;
    test_hwid TEXT := 'TEST_UNBAN_DEBUG_HWID';
    ban_id UUID;
    check_result RECORD;
    lift_result BOOLEAN;
BEGIN
    RAISE NOTICE '=== Testing Global Ban Functions ===';
    
    -- Clean up any existing test data first
    DELETE FROM global_bans WHERE hardware_id = test_hwid;
    
    -- Test 1: Create a global ban
    RAISE NOTICE 'Step 1: Creating test ban...';
    SELECT add_global_ban(
        test_ip,
        test_hwid,
        'Test ban for debugging unban issue',
        NULL
    ) INTO ban_id;
    
    RAISE NOTICE 'Created ban with ID: %', ban_id;
    
    -- Test 2: Check if the ban exists
    RAISE NOTICE 'Step 2: Checking if ban exists...';
    SELECT * INTO check_result FROM is_globally_banned(test_ip, test_hwid);
    RAISE NOTICE 'Ban check result - Banned: %, Reason: %', check_result.banned, check_result.ban_reason;
    
    -- Test 3: Lift the ban
    RAISE NOTICE 'Step 3: Lifting the ban...';
    SELECT lift_global_ban(
        test_ip,
        test_hwid,
        NULL,
        'Test ban lifted for debugging'
    ) INTO lift_result;
    
    RAISE NOTICE 'Lift ban result: %', lift_result;
    
    -- Test 4: Verify the ban was lifted
    RAISE NOTICE 'Step 4: Verifying ban was lifted...';
    SELECT * INTO check_result FROM is_globally_banned(test_ip, test_hwid);
    RAISE NOTICE 'Post-lift check - Banned: %, Reason: %', check_result.banned, check_result.ban_reason;
    
    -- Clean up
    DELETE FROM global_bans WHERE hardware_id = test_hwid;
    RAISE NOTICE 'Test completed and cleaned up.';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: %', SQLERRM;
        -- Clean up on error
        DELETE FROM global_bans WHERE hardware_id = test_hwid;
END $$;

-- Step 4: Check for any existing global bans
SELECT 
    'Existing Bans' as test_step,
    COUNT(*) as total_bans,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_bans,
    COUNT(CASE WHEN is_active = false THEN 1 END) as inactive_bans
FROM global_bans;

-- Step 5: Show a few sample ban records if they exist
SELECT 
    'Sample Bans' as test_step,
    id,
    ip_address,
    hardware_id,
    ban_reason,
    is_active,
    banned_at,
    lifted_at
FROM global_bans 
ORDER BY banned_at DESC 
LIMIT 5;
