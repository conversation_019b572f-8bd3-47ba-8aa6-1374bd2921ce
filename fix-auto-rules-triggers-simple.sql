-- Simple fix for auto rules trigger conflicts
-- Run this if you get "trigger already exists" errors

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS trigger_update_auto_rules_updated_at ON blacklist_auto_rules;
DROP TRIGGER IF EXISTS trigger_update_validation_patterns_updated_at ON validation_patterns;
DROP TRIGGER IF EXISTS trigger_update_whitelist_updated_at ON blacklist_whitelist;

-- Recreate the function (in case it needs updating)
CREATE OR REPLACE FUNCTION update_auto_rules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_update_auto_rules_updated_at
    BEFORE UPDATE ON blacklist_auto_rules
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_rules_updated_at();

CREATE TRIGGER trigger_update_validation_patterns_updated_at
    BEFORE UPDATE ON validation_patterns
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_rules_updated_at();

CREATE TRIGGER trigger_update_whitelist_updated_at
    BEFORE UPDATE ON blacklist_whitelist
    FOR EACH ROW
    EXECUTE FUNCTION update_auto_rules_updated_at();

-- Success message
SELECT 'Auto rules triggers have been fixed successfully!' as status;
