-- Migration: Add original_expires_at column to preserve original expiration dates
-- This allows manual expiration/reactivation without losing the intended expiration date

-- Add the new column
ALTER TABLE license_keys ADD COLUMN original_expires_at TIMESTAMP WITH TIME ZONE;

-- Migrate existing data - copy current expires_at to original_expires_at
UPDATE license_keys 
SET original_expires_at = expires_at 
WHERE expires_at IS NOT NULL;

-- Add comment to document the purpose
COMMENT ON COLUMN license_keys.original_expires_at IS 'Stores the original intended expiration date, preserved when manually expiring/reactivating keys';

-- Create index for performance
CREATE INDEX idx_license_keys_original_expires_at ON license_keys(original_expires_at);

-- Update the validation function to handle the new logic
CREATE OR REPLACE FUNCTION validate_license_key(
    key_to_validate TEXT,
    hardware_id TEXT DEFAULT NULL
)
RETURNS TABLE(
    valid BOOLEAN,
    message TEXT,
    key_data JSONB
) AS $$
DECLARE
    key_record RECORD;
    result_valid BOOLEAN := FALSE;
    result_message TEXT := 'Invalid license key';
    result_data JSONB := '{}'::jsonb;
BEGIN
    -- Get license key record
    SELECT * INTO key_record
    FROM license_keys
    WHERE license_key = key_to_validate;

    IF NOT FOUND THEN
        result_message := 'License key not found';
    ELSE
        -- Check if key is revoked
        IF COALESCE(key_record.is_revoked, FALSE) = TRUE THEN
            result_message := 'License key has been revoked';
        -- Check expiration (using current expires_at, not original)
        ELSIF key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW() THEN
            result_message := 'License key has expired';
        -- Check if key is active
        ELSIF COALESCE(key_record.is_active, TRUE) = FALSE THEN
            result_message := 'License key has been suspended';
        -- Check hardware binding
        ELSIF hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NOT NULL
              AND key_record.bound_hardware_id != hardware_id THEN
            -- Revoke the key for security
            UPDATE license_keys
            SET is_revoked = TRUE,
                revoked_at = NOW(),
                revoked_reason = 'Hardware ID mismatch detected'
            WHERE id = key_record.id;
            
            result_message := 'License key has been revoked due to hardware ID mismatch';
        -- Check usage limits
        ELSIF key_record.max_uses IS NOT NULL AND key_record.current_uses >= key_record.max_uses THEN
            result_message := 'License key usage limit exceeded';
        ELSE
            -- Key is valid
            result_valid := TRUE;
            result_message := 'License key validated successfully';
            
            -- Update usage statistics
            UPDATE license_keys
            SET current_uses = current_uses + 1,
                updated_at = NOW()
            WHERE id = key_record.id;
            
            -- Bind hardware ID if not already bound
            IF hardware_id IS NOT NULL AND key_record.bound_hardware_id IS NULL THEN
                UPDATE license_keys
                SET bound_hardware_id = hardware_id,
                    bound_at = NOW()
                WHERE id = key_record.id;
            END IF;
        END IF;
        
        -- Build key data response
        result_data := jsonb_build_object(
            'id', key_record.id,
            'tier', key_record.tier,
            'expires_at', key_record.expires_at,
            'original_expires_at', key_record.original_expires_at,
            'max_uses', key_record.max_uses,
            'current_uses', CASE WHEN result_valid THEN key_record.current_uses + 1 ELSE key_record.current_uses END,
            'is_unlimited', (key_record.max_uses IS NULL),
            'is_active', COALESCE(key_record.is_active, TRUE),
            'is_revoked', COALESCE(key_record.is_revoked, FALSE),
            'is_expired', (key_record.expires_at IS NOT NULL AND key_record.expires_at <= NOW()),
            'is_usage_limit_reached', (key_record.max_uses IS NOT NULL AND key_record.current_uses >= key_record.max_uses),
            'bound_hardware_id', key_record.bound_hardware_id,
            'created_at', key_record.created_at
        );
    END IF;
    
    RETURN QUERY SELECT result_valid, result_message, result_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION validate_license_key(TEXT, TEXT) TO anon, authenticated;

-- Create helper function to manually expire a license key (preserving original expiration)
CREATE OR REPLACE FUNCTION manually_expire_license_key(key_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    key_record RECORD;
BEGIN
    -- Get the current key data
    SELECT * INTO key_record FROM license_keys WHERE id = key_id;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Smart logic to determine original expiration:
    -- If original_expires_at is already set, use it
    -- If not set, we need to determine what the original state was
    IF key_record.original_expires_at IS NULL THEN
        -- If the key was updated significantly after creation (>5 minutes),
        -- it was likely originally "Never" expire and later edited
        IF EXTRACT(EPOCH FROM (key_record.updated_at - key_record.created_at)) / 60 > 5 THEN
            -- Key was likely originally "Never" expire
            UPDATE license_keys
            SET original_expires_at = NULL,
                expires_at = NOW(),
                updated_at = NOW()
            WHERE id = key_id;
        ELSE
            -- Key was likely created with its current expiration date
            UPDATE license_keys
            SET original_expires_at = key_record.expires_at,
                expires_at = NOW(),
                updated_at = NOW()
            WHERE id = key_id;
        END IF;
    ELSE
        -- original_expires_at is already set, just expire the key
        UPDATE license_keys
        SET expires_at = NOW(),
            updated_at = NOW()
        WHERE id = key_id;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to reactivate a license key (restore original expiration)
CREATE OR REPLACE FUNCTION reactivate_license_key(key_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE license_keys
    SET expires_at = original_expires_at,
        is_active = TRUE,
        is_revoked = FALSE,
        updated_at = NOW()
    WHERE id = key_id;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to reactivate a license key and set to "Never" expire
CREATE OR REPLACE FUNCTION reactivate_license_key_as_never(key_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE license_keys
    SET expires_at = NULL,
        is_active = TRUE,
        is_revoked = FALSE,
        updated_at = NOW()
    WHERE id = key_id;

    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions for helper functions
GRANT EXECUTE ON FUNCTION manually_expire_license_key(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION reactivate_license_key(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION reactivate_license_key_as_never(UUID) TO authenticated;
