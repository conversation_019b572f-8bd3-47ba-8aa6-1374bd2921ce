-- Add onboarding fields to user_profiles table
-- Run this in your Supabase SQL editor

-- Add onboarding fields to existing user_profiles table
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS age INTEGER,
ADD COLUMN IF NOT EXISTS username VARCHAR(50) UNIQUE,
ADD COLUMN IF NOT EXISTS usage_reason TEXT,
ADD COLUMN IF NOT EXISTS onboarding_completed_at TIMESTAMP WITH TIME ZONE;

-- Create index for faster username lookups
CREATE INDEX IF NOT EXISTS idx_user_profiles_username ON user_profiles(username);

-- Create index for onboarding status
CREATE INDEX IF NOT EXISTS idx_user_profiles_onboarding ON user_profiles(onboarding_completed);

-- Update RLS policies to include onboarding fields
-- Users can read their own profile including onboarding data
DROP POLICY IF EXISTS "Users can view their own profile" ON user_profiles;
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

-- Users can update their own profile including onboarding data
DROP POLICY IF EXISTS "Users can update their own profile" ON user_profiles;
CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can insert their own profile
DROP POLICY IF EXISTS "Users can insert their own profile" ON user_profiles;
CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to check if username is available
CREATE OR REPLACE FUNCTION is_username_available(check_username TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if username exists (case insensitive)
    RETURN NOT EXISTS (
        SELECT 1 FROM user_profiles 
        WHERE LOWER(username) = LOWER(check_username)
    );
END;
$$;

-- Function to complete onboarding
CREATE OR REPLACE FUNCTION complete_onboarding(
    user_uuid UUID,
    p_age INTEGER,
    p_username VARCHAR(50),
    p_usage_reason TEXT
)
RETURNS user_profiles
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result user_profiles;
BEGIN
    -- Validate age requirement (16+)
    IF p_age < 16 THEN
        RAISE EXCEPTION 'You must be at least 16 years old to use AuthMe';
    END IF;

    -- Validate username availability (case insensitive)
    IF NOT is_username_available(p_username) THEN
        RAISE EXCEPTION 'Username is already taken';
    END IF;

    -- Insert or update user profile with onboarding data
    INSERT INTO user_profiles (
        user_id,
        age,
        username,
        usage_reason,
        onboarding_completed,
        onboarding_completed_at,
        user_role,
        reseller_tier,
        commission_rate,
        is_active_reseller,
        created_at,
        updated_at
    )
    VALUES (
        user_uuid,
        p_age,
        p_username,
        p_usage_reason,
        TRUE,
        NOW(),
        'user',
        'basic',
        0.00,
        FALSE,
        NOW(),
        NOW()
    )
    ON CONFLICT (user_id) DO UPDATE SET
        age = EXCLUDED.age,
        username = EXCLUDED.username,
        usage_reason = EXCLUDED.usage_reason,
        onboarding_completed = TRUE,
        onboarding_completed_at = NOW(),
        updated_at = NOW()
    RETURNING * INTO result;

    RETURN result;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION is_username_available(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION complete_onboarding(UUID, INTEGER, VARCHAR(50), TEXT) TO authenticated;

-- Create a view for onboarding status check
CREATE OR REPLACE VIEW user_onboarding_status AS
SELECT
    up.user_id,
    up.onboarding_completed,
    up.username,
    up.age,
    up.usage_reason,
    up.onboarding_completed_at,
    au.email
FROM user_profiles up
JOIN auth.users au ON up.user_id = au.id
WHERE up.user_id = auth.uid(); -- Built-in RLS filtering

-- Grant access to the view
GRANT SELECT ON user_onboarding_status TO authenticated;
