-- Quick fix for ambiguous column reference in update_validation_pattern function
-- This fixes the immediate issue preventing auto rules from working

-- Update the update_validation_pattern function to fix ambiguous column references
CREATE OR REPLACE FUNCTION update_validation_pattern(
    pattern_ip INET DEFAULT NULL,
    pattern_hwid TEXT DEFAULT NULL,
    pattern_region TEXT DEFAULT NULL,
    pattern_license_key_id UUID DEFAULT NULL,
    pattern_application_id UUID DEFAULT NULL,
    validation_success BOOLEAN DEFAULT TRUE,
    validation_error TEXT DEFAULT NULL
)
RETURNS VOID AS $$
DECLARE
    pattern_key TEXT;
    current_pattern RECORD;
    new_failed_count INTEGER;
    new_total_count INTEGER;
    new_failure_rate NUMERIC;
    new_frequency NUMERIC;
    time_window INTERVAL := '1 hour';
BEGIN
    -- Generate pattern key based on available identifiers
    pattern_key := COALESCE(
        pattern_ip::text,
        pattern_hwid,
        pattern_region,
        'unknown'
    );

    -- Update IP patterns
    IF pattern_ip IS NOT NULL THEN
        -- Get current pattern or create new one
        SELECT * INTO current_pattern
        FROM validation_patterns vp
        WHERE vp.pattern_type = 'ip'
          AND vp.ip_address = pattern_ip
          AND (vp.application_id = pattern_application_id OR (vp.application_id IS NULL AND pattern_application_id IS NULL))
        ORDER BY vp.updated_at DESC
        LIMIT 1;

        -- Calculate new values
        new_total_count := COALESCE(current_pattern.total_validations, 0) + 1;
        new_failed_count := COALESCE(current_pattern.failed_validations, 0) + CASE WHEN validation_success THEN 0 ELSE 1 END;
        new_failure_rate := CASE WHEN new_total_count > 0 THEN (new_failed_count::NUMERIC / new_total_count::NUMERIC) * 100 ELSE 0 END;
        
        -- Calculate frequency (validations per hour)
        new_frequency := CASE 
            WHEN current_pattern.created_at IS NOT NULL THEN
                new_total_count::NUMERIC / GREATEST(EXTRACT(EPOCH FROM (NOW() - current_pattern.created_at)) / 3600, 0.01)
            ELSE new_total_count::NUMERIC
        END;

        -- Insert or update pattern
        INSERT INTO validation_patterns (
            pattern_key,
            pattern_type,
            ip_address,
            total_validations,
            failed_validations,
            failure_rate,
            validation_frequency,
            last_validation_success,
            last_validation_error,
            application_id,
            created_at,
            updated_at
        ) VALUES (
            pattern_key,
            'ip',
            pattern_ip,
            new_total_count,
            new_failed_count,
            new_failure_rate,
            new_frequency,
            validation_success,
            validation_error,
            pattern_application_id,
            COALESCE(current_pattern.created_at, NOW()),
            NOW()
        )
        ON CONFLICT (pattern_key, pattern_type)
        DO UPDATE SET
            total_validations = new_total_count,
            failed_validations = new_failed_count,
            failure_rate = new_failure_rate,
            validation_frequency = new_frequency,
            last_validation_success = validation_success,
            last_validation_error = validation_error,
            application_id = pattern_application_id,
            updated_at = NOW();
    END IF;

    -- Update HWID patterns
    IF pattern_hwid IS NOT NULL THEN
        -- Get current HWID pattern
        SELECT * INTO current_pattern
        FROM validation_patterns vp
        WHERE vp.pattern_type = 'hwid'
          AND vp.hardware_id = pattern_hwid
          AND (vp.application_id = pattern_application_id OR (vp.application_id IS NULL AND pattern_application_id IS NULL))
        ORDER BY vp.updated_at DESC
        LIMIT 1;

        -- Update unique license keys count if we have a license key
        INSERT INTO validation_patterns (
            pattern_key,
            pattern_type,
            hardware_id,
            total_validations,
            failed_validations,
            unique_license_keys,
            last_validation_success,
            last_validation_error,
            application_id,
            created_at,
            updated_at
        ) VALUES (
            pattern_hwid,
            'hwid',
            pattern_hwid,
            COALESCE(current_pattern.total_validations, 0) + 1,
            COALESCE(current_pattern.failed_validations, 0) + CASE WHEN validation_success THEN 0 ELSE 1 END,
            COALESCE(current_pattern.unique_license_keys, 0) + CASE WHEN pattern_license_key_id IS NOT NULL THEN 1 ELSE 0 END,
            validation_success,
            validation_error,
            pattern_application_id,
            COALESCE(current_pattern.created_at, NOW()),
            NOW()
        )
        ON CONFLICT (pattern_key, pattern_type)
        DO UPDATE SET
            total_validations = validation_patterns.total_validations + 1,
            failed_validations = validation_patterns.failed_validations + CASE WHEN validation_success THEN 0 ELSE 1 END,
            unique_license_keys = GREATEST(validation_patterns.unique_license_keys, COALESCE(current_pattern.unique_license_keys, 0) + CASE WHEN pattern_license_key_id IS NOT NULL THEN 1 ELSE 0 END),
            last_validation_success = validation_success,
            last_validation_error = validation_error,
            application_id = pattern_application_id,
            updated_at = NOW();
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT EXECUTE ON FUNCTION update_validation_pattern(INET, TEXT, TEXT, UUID, UUID, BOOLEAN, TEXT) TO authenticated;

-- Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Fixed ambiguous column reference in update_validation_pattern function';
END $$;
