-- Migration to add blur_credentials column to user_preferences table
-- Run this script if you already have an existing user_preferences table

-- Step 1: Add the new blur_credentials column with default value FALSE
ALTER TABLE user_preferences
ADD COLUMN IF NOT EXISTS blur_credentials BOOLEAN DEFAULT FALSE;

-- Step 2: Drop existing functions (required to change return types)
DROP FUNCTION IF EXISTS get_user_preferences(UUID);
DROP FUNCTION IF EXISTS upsert_user_preferences(UUID, BOOLEAN, BOOLEAN, BOOLEAN, VARCHAR(20), BOOLEAN);

-- Step 3: Recreate get_user_preferences function with new blur_credentials column
CREATE OR REPLACE FUNCTION get_user_preferences(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    blur_enabled BOOLEAN,
    blur_license_keys BOOLEAN,
    blur_users_section BOOLEAN,
    blur_credentials BOOLEAN,
    theme VARCHAR(20),
    notifications_enabled BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Check if user has preferences
    IF EXISTS (SELECT 1 FROM user_preferences WHERE user_preferences.user_id = user_uuid) THEN
        -- Return existing preferences
        RETURN QUERY
        SELECT
            user_preferences.id,
            user_preferences.user_id,
            user_preferences.blur_enabled,
            user_preferences.blur_license_keys,
            user_preferences.blur_users_section,
            user_preferences.blur_credentials,
            user_preferences.theme,
            user_preferences.notifications_enabled,
            user_preferences.created_at,
            user_preferences.updated_at
        FROM user_preferences
        WHERE user_preferences.user_id = user_uuid;
    ELSE
        -- If no preferences exist, return defaults
        RETURN QUERY
        SELECT
            NULL::UUID as id,
            user_uuid as user_id,
            FALSE as blur_enabled,
            FALSE as blur_license_keys,
            FALSE as blur_users_section,
            FALSE as blur_credentials,
            'light'::VARCHAR(20) as theme,
            TRUE as notifications_enabled,
            NOW() as created_at,
            NOW() as updated_at;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Recreate upsert_user_preferences function with new blur_credentials column
CREATE OR REPLACE FUNCTION upsert_user_preferences(
    user_uuid UUID,
    p_blur_enabled BOOLEAN DEFAULT NULL,
    p_blur_license_keys BOOLEAN DEFAULT NULL,
    p_blur_users_section BOOLEAN DEFAULT NULL,
    p_blur_credentials BOOLEAN DEFAULT NULL,
    p_theme VARCHAR(20) DEFAULT NULL,
    p_notifications_enabled BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    blur_enabled BOOLEAN,
    blur_license_keys BOOLEAN,
    blur_users_section BOOLEAN,
    blur_credentials BOOLEAN,
    theme VARCHAR(20),
    notifications_enabled BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    INSERT INTO user_preferences (
        user_id,
        blur_enabled,
        blur_license_keys,
        blur_users_section,
        blur_credentials,
        theme,
        notifications_enabled,
        updated_at
    )
    VALUES (
        user_uuid,
        COALESCE(p_blur_enabled, FALSE),
        COALESCE(p_blur_license_keys, FALSE),
        COALESCE(p_blur_users_section, FALSE),
        COALESCE(p_blur_credentials, FALSE),
        COALESCE(p_theme, 'light'),
        COALESCE(p_notifications_enabled, TRUE),
        NOW()
    )
    ON CONFLICT (user_id) DO UPDATE SET
        blur_enabled = COALESCE(p_blur_enabled, user_preferences.blur_enabled),
        blur_license_keys = COALESCE(p_blur_license_keys, user_preferences.blur_license_keys),
        blur_users_section = COALESCE(p_blur_users_section, user_preferences.blur_users_section),
        blur_credentials = COALESCE(p_blur_credentials, user_preferences.blur_credentials),
        theme = COALESCE(p_theme, user_preferences.theme),
        notifications_enabled = COALESCE(p_notifications_enabled, user_preferences.notifications_enabled),
        updated_at = NOW()
    RETURNING
        user_preferences.id,
        user_preferences.user_id,
        user_preferences.blur_enabled,
        user_preferences.blur_license_keys,
        user_preferences.blur_users_section,
        user_preferences.blur_credentials,
        user_preferences.theme,
        user_preferences.notifications_enabled,
        user_preferences.created_at,
        user_preferences.updated_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Grant permissions on the recreated functions
GRANT EXECUTE ON FUNCTION get_user_preferences(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION upsert_user_preferences(UUID, BOOLEAN, BOOLEAN, BOOLEAN, BOOLEAN, VARCHAR(20), BOOLEAN) TO authenticated;

-- Step 6: Verify the migration worked
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'user_preferences'
    AND column_name = 'blur_credentials';